/**
 * 高程服务
 */
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class ElevationService {
  private readonly logger = new Logger(ElevationService.name);

  async getElevation(lat: number, lon: number) {
    this.logger.log(`获取高程: ${lat}, ${lon}`);
    
    // 简化实现，返回模拟数据
    return {
      success: true,
      elevation: Math.round(Math.random() * 1000), // 随机高程值
      lat,
      lon,
      unit: 'meters'
    };
  }

  async getBatchElevation(coordinates: { lat: number; lon: number }[]) {
    this.logger.log(`批量获取高程: ${coordinates.length} 个点`);
    
    const results = coordinates.map(coord => ({
      lat: coord.lat,
      lon: coord.lon,
      elevation: Math.round(Math.random() * 1000),
      unit: 'meters'
    }));

    return {
      success: true,
      results,
      count: results.length
    };
  }
}
