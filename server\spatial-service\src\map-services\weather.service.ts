/**
 * 天气服务
 */
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class WeatherService {
  private readonly logger = new Logger(WeatherService.name);

  async getCurrentWeather(lat: number, lon: number) {
    this.logger.log(`获取当前天气: ${lat}, ${lon}`);
    
    // 简化实现，返回模拟数据
    return {
      success: true,
      weather: {
        temperature: Math.round(Math.random() * 30 + 5), // 5-35度
        humidity: Math.round(Math.random() * 100),
        pressure: Math.round(Math.random() * 100 + 950),
        description: '晴天',
        windSpeed: Math.round(Math.random() * 20),
        windDirection: Math.round(Math.random() * 360)
      },
      location: { lat, lon },
      timestamp: new Date().toISOString()
    };
  }

  async getForecast(lat: number, lon: number, days: number = 5) {
    this.logger.log(`获取天气预报: ${lat}, ${lon}, ${days}天`);
    
    const forecast = [];
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      
      forecast.push({
        date: date.toISOString().split('T')[0],
        temperature: {
          min: Math.round(Math.random() * 15 + 5),
          max: Math.round(Math.random() * 15 + 20)
        },
        humidity: Math.round(Math.random() * 100),
        description: i % 2 === 0 ? '晴天' : '多云'
      });
    }

    return {
      success: true,
      forecast,
      location: { lat, lon },
      days
    };
  }
}
