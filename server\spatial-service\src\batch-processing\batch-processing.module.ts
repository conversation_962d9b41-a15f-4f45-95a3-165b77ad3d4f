/**
 * 批量数据处理模块
 */
import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { BatchProcessingController } from './batch-processing.controller';
import { BatchProcessingService } from './batch-processing.service';
import { DataImportService } from './data-import.service';
import { DataExportService } from './data-export.service';
import { DataValidationService } from './data-validation.service';
import { BatchJobProcessor } from './batch-job.processor';
import { SpatialFeature } from '../entities/spatial-feature.entity';
import { SpatialLayer } from '../entities/spatial-layer.entity';
import { SpatialProject } from '../entities/spatial-project.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SpatialFeature,
      SpatialLayer,
      SpatialProject
    ]),
    BullModule.registerQueue({
      name: 'batch-processing',
    }),
  ],
  controllers: [BatchProcessingController],
  providers: [
    BatchProcessingService,
    DataImportService,
    DataExportService,
    DataValidationService,
    BatchJobProcessor
  ],
  exports: [
    BatchProcessingService,
    DataImportService,
    DataExportService,
    DataValidationService
  ]
})
export class BatchProcessingModule {}
