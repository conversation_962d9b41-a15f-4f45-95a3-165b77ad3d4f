"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JoinSessionDto = exports.CreateSessionDto = exports.PeerCapabilitiesDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
class PeerCapabilitiesDto {
}
exports.PeerCapabilitiesDto = PeerCapabilitiesDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否支持硬件加速' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PeerCapabilitiesDto.prototype, "hardwareAcceleration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否支持SIMD' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PeerCapabilitiesDto.prototype, "simdSupport", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否支持WebCodecs' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PeerCapabilitiesDto.prototype, "webCodecsSupport", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最大比特率' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], PeerCapabilitiesDto.prototype, "maxBitrate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '首选编解码器列表' }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], PeerCapabilitiesDto.prototype, "preferredCodecs", void 0);
class CreateSessionDto {
}
exports.CreateSessionDto = CreateSessionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '会话名称' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSessionDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '会话描述' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSessionDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '最大参与者数量' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateSessionDto.prototype, "maxParticipants", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '会话类型' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSessionDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '会话配置' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateSessionDto.prototype, "config", void 0);
class JoinSessionDto {
}
exports.JoinSessionDto = JoinSessionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '对等方ID' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JoinSessionDto.prototype, "peerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '对等方能力', type: PeerCapabilitiesDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PeerCapabilitiesDto),
    __metadata("design:type", PeerCapabilitiesDto)
], JoinSessionDto.prototype, "capabilities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '用户信息' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], JoinSessionDto.prototype, "userInfo", void 0);
