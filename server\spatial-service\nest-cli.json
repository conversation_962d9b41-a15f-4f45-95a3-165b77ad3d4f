{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": false, "tsConfigPath": "tsconfig.json", "assets": ["**/*.json", "**/*.md", "**/*.txt", "**/*.sql"], "watchAssets": true, "plugins": [{"name": "@nestjs/swagger", "options": {"classValidatorShim": true, "introspectComments": true}}]}, "projects": {"spatial-service": {"type": "application", "root": "", "entryFile": "main", "sourceRoot": "src", "compilerOptions": {"tsConfigPath": "tsconfig.json"}}}, "generateOptions": {"spec": true, "flat": false}, "monorepo": false, "root": "src"}