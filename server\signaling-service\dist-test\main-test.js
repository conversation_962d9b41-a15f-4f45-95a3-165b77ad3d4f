"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const config_1 = require("@nestjs/config");
const compression = __importStar(require("compression"));
const helmet_1 = __importDefault(require("helmet"));
const signaling_test_module_1 = require("./signaling-test.module");
async function bootstrap() {
    const app = await core_1.NestFactory.create(signaling_test_module_1.SignalingTestModule);
    // 获取配置服务
    const configService = app.get(config_1.ConfigService);
    // 启用CORS
    app.enableCors({
        origin: configService.get('CORS_ORIGIN', '*'),
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    });
    // 安全中间件
    app.use((0, helmet_1.default)({
        crossOriginEmbedderPolicy: false,
        contentSecurityPolicy: false
    }));
    // 压缩中间件
    app.use(compression());
    // 全局验证管道
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true
    }));
    // Swagger API文档
    const config = new swagger_1.DocumentBuilder()
        .setTitle('信令服务 API (测试版)')
        .setDescription('超低延迟WebRTC信令服务API文档 - 测试环境')
        .setVersion('1.0')
        .addTag('signaling', 'WebRTC信令相关接口')
        .addTag('monitoring', '监控相关接口')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api', app, document);
    // 启动服务
    const port = configService.get('PORT', 3001);
    await app.listen(port);
    console.log(`🚀 信令服务已启动，端口: ${port} (测试模式)`);
    console.log(`📚 API文档地址: http://localhost:${port}/api`);
    console.log(`🔌 WebSocket连接地址: ws://localhost:${port}/signaling`);
}
bootstrap().catch(err => {
    console.error('服务启动失败:', err);
    process.exit(1);
});
