"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SessionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionService = void 0;
const common_1 = require("@nestjs/common");
const common_2 = require("@nestjs/common");
const cache_manager_1 = require("@nestjs/cache-manager");
const uuid_1 = require("uuid");
let SessionService = SessionService_1 = class SessionService {
    constructor(cacheManager) {
        this.cacheManager = cacheManager;
        this.logger = new common_1.Logger(SessionService_1.name);
    }
    async createSession(createSessionDto) {
        const sessionId = (0, uuid_1.v4)();
        const session = {
            id: sessionId,
            name: createSessionDto.name,
            description: createSessionDto.description,
            maxParticipants: createSessionDto.maxParticipants || 10,
            type: createSessionDto.type || 'webrtc',
            participants: [],
            createdAt: new Date(),
            updatedAt: new Date(),
            config: createSessionDto.config || {},
        };
        // 缓存会话信息
        await this.cacheManager.set(`session:${sessionId}`, session, 3600000); // 1小时
        this.logger.log(`创建会话: ${sessionId} (${session.name})`);
        return session;
    }
    async getSession(sessionId) {
        const session = await this.cacheManager.get(`session:${sessionId}`);
        return session || null;
    }
    async joinSession(sessionId, joinSessionDto) {
        const session = await this.getSession(sessionId);
        if (!session) {
            throw new Error('会话不存在');
        }
        if (session.participants.length >= session.maxParticipants) {
            throw new Error('会话已满');
        }
        if (session.participants.includes(joinSessionDto.peerId)) {
            throw new Error('已在会话中');
        }
        // 添加参与者
        session.participants.push(joinSessionDto.peerId);
        session.updatedAt = new Date();
        // 更新缓存
        await this.cacheManager.set(`session:${sessionId}`, session, 3600000);
        this.logger.log(`${joinSessionDto.peerId} 加入会话: ${sessionId}`);
        return {
            sessionId,
            participants: session.participants,
            capabilities: joinSessionDto.capabilities,
        };
    }
    async leaveSession(sessionId, peerId) {
        const session = await this.getSession(sessionId);
        if (!session) {
            return;
        }
        const index = session.participants.indexOf(peerId);
        if (index > -1) {
            session.participants.splice(index, 1);
            session.updatedAt = new Date();
            // 更新缓存
            await this.cacheManager.set(`session:${sessionId}`, session, 3600000);
            this.logger.log(`${peerId} 离开会话: ${sessionId}`);
        }
    }
    async getActiveSessions(page = 1, limit = 10) {
        // 这里应该实现分页逻辑，由于使用缓存，需要特殊处理
        // 简化实现，实际项目中可能需要使用数据库
        const sessions = [];
        // 模拟获取活跃会话
        // 实际实现中应该从Redis或数据库中获取
        return {
            sessions,
            total: sessions.length,
            page,
            limit,
            totalPages: Math.ceil(sessions.length / limit),
        };
    }
    async deleteSession(sessionId) {
        await this.cacheManager.del(`session:${sessionId}`);
        this.logger.log(`删除会话: ${sessionId}`);
    }
    async updateSession(sessionId, updates) {
        const session = await this.getSession(sessionId);
        if (!session) {
            return null;
        }
        Object.assign(session, updates, { updatedAt: new Date() });
        await this.cacheManager.set(`session:${sessionId}`, session, 3600000);
        this.logger.log(`更新会话: ${sessionId}`);
        return session;
    }
};
exports.SessionService = SessionService;
exports.SessionService = SessionService = SessionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [Object])
], SessionService);
