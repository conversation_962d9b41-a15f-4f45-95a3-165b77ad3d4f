/**
 * 批量处理控制器
 */
import { Controller, Post, Get, Body, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { BatchProcessingService } from './batch-processing.service';

@ApiTags('批量处理')
@Controller('batch-processing')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class BatchProcessingController {
  constructor(
    private readonly batchProcessingService: BatchProcessingService,
  ) {}

  @Post('import')
  @ApiOperation({ summary: '批量导入数据' })
  @ApiResponse({ status: 200, description: '导入任务已创建' })
  async importData(@Body() importDto: any) {
    return this.batchProcessingService.createImportJob(importDto);
  }

  @Post('export')
  @ApiOperation({ summary: '批量导出数据' })
  @ApiResponse({ status: 200, description: '导出任务已创建' })
  async exportData(@Body() exportDto: any) {
    return this.batchProcessingService.createExportJob(exportDto);
  }

  @Get('jobs/:id')
  @ApiOperation({ summary: '获取任务状态' })
  @ApiResponse({ status: 200, description: '任务状态信息' })
  async getJobStatus(@Param('id') jobId: string) {
    return this.batchProcessingService.getJobStatus(jobId);
  }

  @Get('jobs')
  @ApiOperation({ summary: '获取任务列表' })
  @ApiResponse({ status: 200, description: '任务列表' })
  async getJobs() {
    return this.batchProcessingService.getJobs();
  }
}
