import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import compression from 'compression';
import helmet from 'helmet';
import { SignalingTestModule } from './signaling-test.module';

async function bootstrap() {
  const app = await NestFactory.create(SignalingTestModule);
  
  // 获取配置服务
  const configService = app.get(ConfigService);
  
  // 启用CORS
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*'),
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  });

  // 安全中间件
  app.use(helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: false
  }));

  // 压缩中间件
  app.use(compression());

  // 全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true
  }));

  // Swagger API文档
  const config = new DocumentBuilder()
    .setTitle('信令服务 API (测试版)')
    .setDescription('超低延迟WebRTC信令服务API文档 - 测试环境')
    .setVersion('1.0')
    .addTag('signaling', 'WebRTC信令相关接口')
    .addTag('monitoring', '监控相关接口')
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  // 启动服务
  const port = configService.get('PORT', 3001);
  await app.listen(port);
  
  console.log(`🚀 信令服务已启动，端口: ${port} (测试模式)`);
  console.log(`📚 API文档地址: http://localhost:${port}/api`);
  console.log(`🔌 WebSocket连接地址: ws://localhost:${port}/signaling`);
}

bootstrap().catch(err => {
  console.error('服务启动失败:', err);
  process.exit(1);
});
