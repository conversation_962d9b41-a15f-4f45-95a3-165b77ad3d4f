"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SignalingController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const ultra_low_latency_signaling_service_1 = require("../ultra-low-latency-signaling.service");
const session_service_1 = require("../services/session.service");
const session_dto_1 = require("../dto/session.dto");
let SignalingController = class SignalingController {
    constructor(signalingService, sessionService) {
        this.signalingService = signalingService;
        this.sessionService = sessionService;
    }
    getHealth() {
        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            service: 'signaling-service',
            version: '1.0.0'
        };
    }
    async createSession(createSessionDto) {
        try {
            const session = await this.sessionService.createSession(createSessionDto);
            return {
                success: true,
                data: session,
                message: '会话创建成功'
            };
        }
        catch (error) {
            throw new common_1.HttpException(`创建会话失败: ${error.message}`, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async joinSession(sessionId, joinSessionDto) {
        try {
            const result = await this.sessionService.joinSession(sessionId, joinSessionDto);
            return {
                success: true,
                data: result,
                message: '加入会话成功'
            };
        }
        catch (error) {
            throw new common_1.HttpException(`加入会话失败: ${error.message}`, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getSession(sessionId) {
        try {
            const session = await this.sessionService.getSession(sessionId);
            if (!session) {
                throw new common_1.HttpException('会话不存在', common_1.HttpStatus.NOT_FOUND);
            }
            return {
                success: true,
                data: session
            };
        }
        catch (error) {
            throw new common_1.HttpException(`获取会话信息失败: ${error.message}`, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getSessions(page = 1, limit = 10) {
        try {
            const sessions = await this.sessionService.getActiveSessions(page, limit);
            return {
                success: true,
                data: sessions
            };
        }
        catch (error) {
            throw new common_1.HttpException(`获取会话列表失败: ${error.message}`, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getPeers() {
        try {
            const metrics = this.signalingService.getPerformanceMetrics();
            return {
                success: true,
                data: {
                    connectedPeers: metrics.connectedPeers,
                    activeSessions: metrics.activeSessions,
                    timestamp: new Date().toISOString()
                }
            };
        }
        catch (error) {
            throw new common_1.HttpException(`获取对等方列表失败: ${error.message}`, common_1.HttpStatus.BAD_REQUEST);
        }
    }
};
exports.SignalingController = SignalingController;
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({ summary: '健康检查' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '服务正常运行' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], SignalingController.prototype, "getHealth", null);
__decorate([
    (0, common_1.Post)('sessions'),
    (0, swagger_1.ApiOperation)({ summary: '创建信令会话' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '会话创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [session_dto_1.CreateSessionDto]),
    __metadata("design:returntype", Promise)
], SignalingController.prototype, "createSession", null);
__decorate([
    (0, common_1.Post)('sessions/:sessionId/join'),
    (0, swagger_1.ApiOperation)({ summary: '加入信令会话' }),
    (0, swagger_1.ApiParam)({ name: 'sessionId', description: '会话ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '加入会话成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '会话不存在' }),
    __param(0, (0, common_1.Param)('sessionId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, session_dto_1.JoinSessionDto]),
    __metadata("design:returntype", Promise)
], SignalingController.prototype, "joinSession", null);
__decorate([
    (0, common_1.Get)('sessions/:sessionId'),
    (0, swagger_1.ApiOperation)({ summary: '获取会话信息' }),
    (0, swagger_1.ApiParam)({ name: 'sessionId', description: '会话ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取会话信息成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '会话不存在' }),
    __param(0, (0, common_1.Param)('sessionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SignalingController.prototype, "getSession", null);
__decorate([
    (0, common_1.Get)('sessions'),
    (0, swagger_1.ApiOperation)({ summary: '获取活跃会话列表' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: '页码' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: '每页数量' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取会话列表成功' }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], SignalingController.prototype, "getSessions", null);
__decorate([
    (0, common_1.Get)('peers'),
    (0, swagger_1.ApiOperation)({ summary: '获取在线对等方列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取对等方列表成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SignalingController.prototype, "getPeers", null);
exports.SignalingController = SignalingController = __decorate([
    (0, swagger_1.ApiTags)('signaling'),
    (0, common_1.Controller)('signaling'),
    __metadata("design:paramtypes", [ultra_low_latency_signaling_service_1.UltraLowLatencySignalingService,
        session_service_1.SessionService])
], SignalingController);
