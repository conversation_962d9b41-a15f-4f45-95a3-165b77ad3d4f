/**
 * 健康检查服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SpatialFeature } from '../entities/spatial-feature.entity';
import { SpatialLayer } from '../entities/spatial-layer.entity';
import { SpatialProject } from '../entities/spatial-project.entity';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(
    @InjectRepository(SpatialFeature)
    private spatialFeatureRepository: Repository<SpatialFeature>,
    @InjectRepository(SpatialLayer)
    private spatialLayerRepository: Repository<SpatialLayer>,
    @InjectRepository(SpatialProject)
    private spatialProjectRepository: Repository<SpatialProject>,
  ) {}

  /**
   * 获取详细健康状态
   */
  async getDetailedHealth() {
    const startTime = Date.now();
    
    try {
      const [
        databaseHealth,
        spatialHealth,
        performanceMetrics,
        systemInfo
      ] = await Promise.all([
        this.checkDatabaseHealth(),
        this.checkSpatialServices(),
        this.getPerformanceMetrics(),
        this.getSystemInfo()
      ]);

      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        responseTime: `${responseTime}ms`,
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        uptime: process.uptime(),
        checks: {
          database: databaseHealth,
          spatial: spatialHealth,
          performance: performanceMetrics,
          system: systemInfo
        }
      };
    } catch (error) {
      this.logger.error('健康检查失败:', error);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }

  /**
   * 检查数据库健康状态
   */
  async checkDatabaseHealth() {
    try {
      // 检查数据库连接
      const connectionCheck = await this.spatialFeatureRepository.query('SELECT 1');
      
      // 检查PostGIS扩展
      const postgisCheck = await this.spatialFeatureRepository.query(
        'SELECT PostGIS_Version() as version'
      );
      
      // 获取数据库统计信息
      const [featureCount, layerCount, projectCount] = await Promise.all([
        this.spatialFeatureRepository.count(),
        this.spatialLayerRepository.count(),
        this.spatialProjectRepository.count()
      ]);

      // 检查空间索引
      const spatialIndexCheck = await this.spatialFeatureRepository.query(`
        SELECT 
          schemaname,
          tablename,
          indexname,
          indexdef
        FROM pg_indexes 
        WHERE indexdef LIKE '%USING gist%' 
        AND tablename = 'spatial_features'
      `);

      return {
        status: 'healthy',
        connection: connectionCheck ? 'connected' : 'disconnected',
        postgis: {
          version: postgisCheck[0]?.version || 'unknown',
          available: !!postgisCheck[0]?.version
        },
        statistics: {
          features: featureCount,
          layers: layerCount,
          projects: projectCount
        },
        spatialIndexes: spatialIndexCheck.length,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('数据库健康检查失败:', error);
      return {
        status: 'unhealthy',
        error: error.message,
        lastChecked: new Date().toISOString()
      };
    }
  }

  /**
   * 检查空间服务
   */
  async checkSpatialServices() {
    try {
      // 测试空间查询功能
      const spatialQueryTest = await this.spatialFeatureRepository.query(`
        SELECT ST_AsText(ST_Point(0, 0)) as point
      `);

      // 测试空间分析功能
      const spatialAnalysisTest = await this.spatialFeatureRepository.query(`
        SELECT ST_Area(ST_Buffer(ST_Point(0, 0), 1)) as buffer_area
      `);

      // 检查空间参考系统
      const sridCheck = await this.spatialFeatureRepository.query(`
        SELECT auth_name, auth_srid, srtext 
        FROM spatial_ref_sys 
        WHERE srid = 4326
      `);

      return {
        status: 'healthy',
        spatialQuery: spatialQueryTest ? 'working' : 'failed',
        spatialAnalysis: spatialAnalysisTest ? 'working' : 'failed',
        coordinateSystem: {
          wgs84Available: sridCheck.length > 0,
          defaultSrid: 4326
        },
        capabilities: {
          geometryTypes: ['Point', 'LineString', 'Polygon', 'MultiPoint', 'MultiLineString', 'MultiPolygon'],
          spatialOperations: ['Buffer', 'Intersection', 'Union', 'Difference', 'Contains', 'Intersects'],
          coordinateTransformation: true,
          spatialIndexing: true
        },
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('空间服务检查失败:', error);
      return {
        status: 'unhealthy',
        error: error.message,
        lastChecked: new Date().toISOString()
      };
    }
  }

  /**
   * 获取性能指标
   */
  async getPerformanceMetrics() {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();

      // 数据库性能指标
      const dbStats = await this.spatialFeatureRepository.query(`
        SELECT 
          pg_database_size(current_database()) as db_size,
          pg_stat_get_db_numbackends(oid) as connections
        FROM pg_database 
        WHERE datname = current_database()
      `);

      // 查询性能测试
      const queryStartTime = Date.now();
      await this.spatialFeatureRepository.query('SELECT COUNT(*) FROM spatial_features');
      const queryTime = Date.now() - queryStartTime;

      return {
        status: 'healthy',
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
          external: Math.round(memoryUsage.external / 1024 / 1024), // MB
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system
        },
        database: {
          size: dbStats[0]?.db_size ? Math.round(dbStats[0].db_size / 1024 / 1024) : 0, // MB
          connections: dbStats[0]?.connections || 0,
          queryTime: `${queryTime}ms`
        },
        uptime: Math.round(process.uptime()),
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('性能指标获取失败:', error);
      return {
        status: 'unhealthy',
        error: error.message,
        lastChecked: new Date().toISOString()
      };
    }
  }

  /**
   * 获取系统信息
   */
  private getSystemInfo() {
    return {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      pid: process.pid,
      environment: process.env.NODE_ENV || 'development',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      locale: Intl.DateTimeFormat().resolvedOptions().locale
    };
  }

  /**
   * 检查服务依赖
   */
  async checkDependencies() {
    const dependencies = [];

    // 检查Redis连接（如果配置了）
    if (process.env.REDIS_HOST) {
      try {
        // 这里应该添加Redis连接检查
        dependencies.push({
          name: 'Redis',
          status: 'unknown',
          message: 'Redis检查未实现'
        });
      } catch (error) {
        dependencies.push({
          name: 'Redis',
          status: 'unhealthy',
          error: error.message
        });
      }
    }

    // 检查外部API服务
    const externalServices = [
      { name: 'Geocoding API', envKey: 'GEOCODING_API_KEY' },
      { name: 'Elevation API', envKey: 'ELEVATION_API_KEY' },
      { name: 'Weather API', envKey: 'WEATHER_API_KEY' }
    ];

    for (const service of externalServices) {
      dependencies.push({
        name: service.name,
        status: process.env[service.envKey] ? 'configured' : 'not_configured',
        configured: !!process.env[service.envKey]
      });
    }

    return dependencies;
  }
}
