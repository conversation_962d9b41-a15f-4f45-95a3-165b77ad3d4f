"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PerformanceService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const os = __importStar(require("os"));
let PerformanceService = PerformanceService_1 = class PerformanceService {
    constructor() {
        this.logger = new common_1.Logger(PerformanceService_1.name);
        this.alerts = [];
        this.startTime = new Date();
        this.initializeMetrics();
    }
    initializeMetrics() {
        this.systemMetrics = {
            cpu: { usage: 0, loadAverage: [] },
            memory: { total: 0, used: 0, free: 0, usage: 0 },
            network: { connections: 0, bytesIn: 0, bytesOut: 0 },
            uptime: 0,
        };
        this.connectionStats = {
            total: 0,
            active: 0,
            idle: 0,
            failed: 0,
            avgLatency: 0,
        };
    }
    updateSystemMetrics() {
        try {
            // CPU指标
            const cpus = os.cpus();
            let totalIdle = 0;
            let totalTick = 0;
            cpus.forEach(cpu => {
                for (const type in cpu.times) {
                    totalTick += cpu.times[type];
                }
                totalIdle += cpu.times.idle;
            });
            const idle = totalIdle / cpus.length;
            const total = totalTick / cpus.length;
            const usage = 100 - ~~(100 * idle / total);
            this.systemMetrics.cpu = {
                usage,
                loadAverage: os.loadavg(),
            };
            // 内存指标
            const totalMem = os.totalmem();
            const freeMem = os.freemem();
            const usedMem = totalMem - freeMem;
            this.systemMetrics.memory = {
                total: totalMem,
                used: usedMem,
                free: freeMem,
                usage: (usedMem / totalMem) * 100,
            };
            // 系统运行时间
            this.systemMetrics.uptime = os.uptime();
            // 检查告警条件
            this.checkAlerts();
        }
        catch (error) {
            this.logger.error('更新系统指标失败:', error);
        }
    }
    checkAlerts() {
        const now = new Date();
        // CPU使用率告警
        if (this.systemMetrics.cpu.usage > 80) {
            this.addAlert('warning', `CPU使用率过高: ${this.systemMetrics.cpu.usage.toFixed(2)}%`, now);
        }
        // 内存使用率告警
        if (this.systemMetrics.memory.usage > 85) {
            this.addAlert('warning', `内存使用率过高: ${this.systemMetrics.memory.usage.toFixed(2)}%`, now);
        }
        // 连接数告警
        if (this.connectionStats.active > 1000) {
            this.addAlert('warning', `活跃连接数过多: ${this.connectionStats.active}`, now);
        }
        // 延迟告警
        if (this.connectionStats.avgLatency > 100) {
            this.addAlert('warning', `平均延迟过高: ${this.connectionStats.avgLatency}ms`, now);
        }
    }
    addAlert(type, message, timestamp) {
        const existingAlert = this.alerts.find(alert => alert.message === message && !alert.resolved);
        if (!existingAlert) {
            const alert = {
                id: `alert_${Date.now()}`,
                type,
                message,
                timestamp,
                resolved: false,
            };
            this.alerts.push(alert);
            this.logger.warn(`新告警: ${message}`);
            // 保持告警列表大小
            if (this.alerts.length > 100) {
                this.alerts = this.alerts.slice(-100);
            }
        }
    }
    getSystemMetrics() {
        return { ...this.systemMetrics };
    }
    getConnectionStats() {
        return { ...this.connectionStats };
    }
    updateConnectionStats(stats) {
        Object.assign(this.connectionStats, stats);
    }
    getActiveAlerts() {
        return this.alerts.filter(alert => !alert.resolved);
    }
    resolveAlert(alertId) {
        const alert = this.alerts.find(a => a.id === alertId);
        if (alert) {
            alert.resolved = true;
            this.logger.log(`告警已解决: ${alert.message}`);
        }
    }
    getServiceStatus() {
        const uptime = Date.now() - this.startTime.getTime();
        const activeAlerts = this.getActiveAlerts();
        return {
            status: activeAlerts.length === 0 ? 'healthy' : 'warning',
            uptime: uptime,
            startTime: this.startTime,
            activeAlerts: activeAlerts.length,
            systemHealth: {
                cpu: this.systemMetrics.cpu.usage < 80 ? 'good' : 'warning',
                memory: this.systemMetrics.memory.usage < 85 ? 'good' : 'warning',
                connections: this.connectionStats.active < 1000 ? 'good' : 'warning',
            },
        };
    }
};
exports.PerformanceService = PerformanceService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_10_SECONDS),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], PerformanceService.prototype, "updateSystemMetrics", null);
exports.PerformanceService = PerformanceService = PerformanceService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], PerformanceService);
