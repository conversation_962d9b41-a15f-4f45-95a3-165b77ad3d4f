"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UltraLowLatencySignalingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UltraLowLatencySignalingService = void 0;
/**
 * 超低延迟信令服务
 * 优化WebRTC信令过程以减少连接建立时间
 */
const common_1 = require("@nestjs/common");
const ioredis_1 = require("ioredis");
const session_service_1 = require("./services/session.service");
const performance_service_1 = require("./services/performance.service");
const metrics_service_1 = require("./services/metrics.service");
let UltraLowLatencySignalingService = UltraLowLatencySignalingService_1 = class UltraLowLatencySignalingService {
    constructor(sessionService, performanceService, metricsService) {
        this.sessionService = sessionService;
        this.performanceService = performanceService;
        this.metricsService = metricsService;
        this.logger = new common_1.Logger(UltraLowLatencySignalingService_1.name);
        this.peers = new Map();
        this.sessions = new Map();
        // 只在非测试环境初始化Redis
        if (process.env.NODE_ENV !== 'test' && process.env.REDIS_ENABLED !== 'false') {
            try {
                this.redis = new ioredis_1.Redis({
                    host: process.env.REDIS_HOST || 'localhost',
                    port: parseInt(process.env.REDIS_PORT || '6379'),
                    maxRetriesPerRequest: 3,
                    lazyConnect: true
                });
            }
            catch (error) {
                console.warn('Redis初始化失败，将使用内存存储:', error.message);
            }
        }
        this.latencyMonitor = new LatencyMonitor();
        this.startPerformanceMonitoring();
    }
    setServer(server) {
        this.server = server;
        this.afterInit(server);
    }
    afterInit(server) {
        this.logger.log('超低延迟信令服务已启动');
        // 配置Socket.IO优化
        try {
            if (server.engine) {
                server.engine.generateId = () => {
                    // 使用更短的ID以减少传输开销
                    return Math.random().toString(36).substring(2, 15);
                };
                // 启用二进制传输优化
                server.engine.compression = false;
                server.engine.perMessageDeflate = false;
            }
        }
        catch (error) {
            this.logger.warn('无法设置Socket.IO引擎选项:', error.message);
        }
    }
    async handleConnection(client) {
        const peerId = client.id;
        const region = this.detectRegion(client);
        this.logger.log(`新连接: ${peerId} (区域: ${region})`);
        // 创建对等连接记录
        const peerConnection = {
            id: peerId,
            socket: client,
            lastSeen: Date.now(),
            latency: 0,
            region,
            capabilities: await this.detectCapabilities(client)
        };
        this.peers.set(peerId, peerConnection);
        // 发送优化配置
        client.emit('config', {
            enableBinaryTransport: true,
            enableCompression: false,
            heartbeatInterval: 1000,
            maxRetries: 3,
            timeoutMs: 5000
        });
        // 开始延迟监控
        this.startLatencyMonitoring(client);
        client.on('disconnect', () => {
            this.handleDisconnection(peerId);
        });
    }
    async handleJoinSession(client, data) {
        const peerId = client.id;
        const { sessionId, capabilities } = data;
        this.logger.log(`${peerId} 加入会话: ${sessionId}`);
        // 更新对等连接能力
        const peer = this.peers.get(peerId);
        if (peer) {
            peer.capabilities = capabilities;
        }
        // 添加到会话
        if (!this.sessions.has(sessionId)) {
            this.sessions.set(sessionId, new Set());
        }
        this.sessions.get(sessionId).add(peerId);
        // 缓存会话信息到Redis (如果可用)
        if (this.redis) {
            try {
                await this.redis.sadd(`session:${sessionId}`, peerId);
                await this.redis.expire(`session:${sessionId}`, 3600); // 1小时过期
            }
            catch (error) {
                console.warn('Redis操作失败:', error.message);
            }
        }
        // 通知会话中的其他对等方
        const sessionPeers = this.sessions.get(sessionId);
        for (const otherPeerId of sessionPeers) {
            if (otherPeerId !== peerId) {
                const otherPeer = this.peers.get(otherPeerId);
                if (otherPeer) {
                    otherPeer.socket.emit('peer-joined', {
                        peerId,
                        capabilities,
                        region: peer?.region
                    });
                }
            }
        }
        // 发送现有对等方列表
        const existingPeers = Array.from(sessionPeers)
            .filter(id => id !== peerId)
            .map(id => {
            const p = this.peers.get(id);
            return p ? {
                peerId: id,
                capabilities: p.capabilities,
                region: p.region,
                latency: p.latency
            } : null;
        })
            .filter(Boolean);
        client.emit('session-peers', existingPeers);
    }
    async handleLeaveSession(client, data) {
        const peerId = client.id;
        const { sessionId } = data;
        this.logger.log(`${peerId} 离开会话: ${sessionId}`);
        // 从会话中移除
        const sessionPeers = this.sessions.get(sessionId);
        if (sessionPeers) {
            sessionPeers.delete(peerId);
            // 通知其他对等方
            for (const otherPeerId of sessionPeers) {
                const otherPeer = this.peers.get(otherPeerId);
                if (otherPeer) {
                    otherPeer.socket.emit('peer-left', { peerId });
                }
            }
            // 从Redis中移除 (如果可用)
            if (this.redis) {
                try {
                    await this.redis.srem(`session:${sessionId}`, peerId);
                }
                catch (error) {
                    console.warn('Redis操作失败:', error.message);
                }
            }
        }
        // 更新会话服务
        await this.sessionService.leaveSession(sessionId, peerId);
    }
    handleCapabilityResponse(client, capabilities) {
        const peer = this.peers.get(client.id);
        if (peer) {
            peer.capabilities = capabilities;
            this.logger.log(`更新对等方能力: ${client.id}`);
        }
    }
    async handleSignal(client, message) {
        const startTime = performance.now();
        const { peerId: targetPeerId, type, data, sessionId } = message;
        // 验证会话
        const sessionPeers = this.sessions.get(sessionId);
        if (!sessionPeers || !sessionPeers.has(client.id)) {
            client.emit('error', { message: '未加入会话' });
            return;
        }
        // 查找目标对等方
        const targetPeer = this.peers.get(targetPeerId);
        if (!targetPeer) {
            client.emit('error', { message: '目标对等方不存在' });
            return;
        }
        // 优化信令消息
        const optimizedMessage = await this.optimizeSignalingMessage({
            ...message,
            fromPeerId: client.id,
            timestamp: Date.now()
        });
        // 直接转发到目标对等方
        targetPeer.socket.emit('signal', optimizedMessage);
        // 记录信令延迟
        const processingTime = performance.now() - startTime;
        this.latencyMonitor.recordSignalingLatency(processingTime);
        this.metricsService.recordLatency(processingTime);
        this.metricsService.recordThroughput(1, JSON.stringify(optimizedMessage).length);
        // 如果是ICE候选，已在optimizeSignalingMessage中处理
    }
    handlePing(client, timestamp) {
        // 立即响应ping以测量延迟
        client.emit('pong', timestamp);
        // 更新对等方延迟
        const peer = this.peers.get(client.id);
        if (peer) {
            peer.lastSeen = Date.now();
            peer.latency = Date.now() - timestamp;
        }
    }
    async optimizeSignalingMessage(message) {
        // 压缩SDP以减少传输大小
        if (message.type === 'offer' || message.type === 'answer') {
            message.data.sdp = this.compressSDP(message.data.sdp);
        }
        // 优化ICE候选
        if (message.type === 'ice-candidate' && message.data.candidate) {
            message.data = this.optimizeICECandidate(message.data);
        }
        return message;
    }
    compressSDP(sdp) {
        // 移除不必要的属性以减少SDP大小
        return sdp
            .replace(/a=rtcp-fb:.*\r\n/g, '') // 移除RTCP反馈
            .replace(/a=fmtp:.*useinbandfec=1.*\r\n/g, '') // 移除FEC
            .replace(/a=fmtp:.*usedtx=1.*\r\n/g, '') // 移除DTX
            .replace(/a=ssrc-group:.*\r\n/g, '') // 移除SSRC组
            .replace(/a=msid-semantic:.*\r\n/g, ''); // 移除语义
    }
    optimizeICECandidate(candidate) {
        // 优先选择UDP候选
        if (candidate.candidate && candidate.candidate.includes('udp')) {
            candidate.priority = candidate.priority || 0;
            candidate.priority += 1000; // 提高UDP优先级
        }
        // 优先选择本地候选
        if (candidate.candidate && candidate.candidate.includes('typ host')) {
            candidate.priority = candidate.priority || 0;
            candidate.priority += 2000; // 提高本地候选优先级
        }
        return candidate;
    }
    startLatencyMonitoring(client) {
        const interval = setInterval(() => {
            const peer = this.peers.get(client.id);
            if (!peer) {
                clearInterval(interval);
                return;
            }
            // 发送ping测试延迟
            const timestamp = Date.now();
            client.emit('ping', timestamp);
            // 检查连接健康状态
            if (Date.now() - peer.lastSeen > 10000) { // 10秒无响应
                this.logger.warn(`对等方 ${client.id} 可能已断开连接`);
                client.disconnect();
                clearInterval(interval);
            }
        }, 1000); // 每秒ping一次
    }
    detectRegion(client) {
        // 根据IP地址检测区域
        const clientIP = client.handshake.address;
        // 这里应该实现实际的IP地理位置检测
        return 'default';
    }
    async detectCapabilities(client) {
        // 从客户端获取能力信息
        return new Promise((resolve) => {
            client.emit('capability-request');
            const timeout = setTimeout(() => {
                resolve({
                    hardwareAcceleration: false,
                    simdSupport: false,
                    webCodecsSupport: false,
                    maxBitrate: 1000000,
                    preferredCodecs: ['opus', 'vp8']
                });
            }, 1000);
            client.once('capabilities', (capabilities) => {
                clearTimeout(timeout);
                resolve(capabilities);
            });
        });
    }
    handleDisconnection(peerId) {
        this.logger.log(`连接断开: ${peerId}`);
        // 从所有会话中移除
        for (const [sessionId, sessionPeers] of this.sessions) {
            if (sessionPeers.has(peerId)) {
                sessionPeers.delete(peerId);
                // 通知会话中的其他对等方
                for (const otherPeerId of sessionPeers) {
                    const otherPeer = this.peers.get(otherPeerId);
                    if (otherPeer) {
                        otherPeer.socket.emit('peer-left', { peerId });
                    }
                }
                // 从Redis中移除 (如果可用)
                if (this.redis) {
                    try {
                        this.redis.srem(`session:${sessionId}`, peerId);
                    }
                    catch (error) {
                        console.warn('Redis操作失败:', error.message);
                    }
                }
            }
        }
        // 移除对等连接记录
        this.peers.delete(peerId);
    }
    startPerformanceMonitoring() {
        setInterval(() => {
            const stats = {
                connectedPeers: this.peers.size,
                activeSessions: this.sessions.size,
                averageLatency: this.latencyMonitor.getAverageLatency(),
                signalingLatency: this.latencyMonitor.getSignalingLatency()
            };
            this.logger.log(`性能统计: ${JSON.stringify(stats)}`);
            // 更新性能服务
            this.performanceService.updateConnectionStats({
                total: this.peers.size,
                active: this.peers.size,
                idle: 0,
                failed: 0,
                avgLatency: this.latencyMonitor.getAverageLatency()
            });
            // 发送统计信息到监控系统
            if (this.server) {
                this.server.emit('performance-stats', stats);
            }
        }, 10000); // 每10秒报告一次
    }
    getPerformanceMetrics() {
        return {
            connectedPeers: this.peers.size,
            activeSessions: this.sessions.size,
            latencyStats: this.latencyMonitor.getStatistics()
        };
    }
};
exports.UltraLowLatencySignalingService = UltraLowLatencySignalingService;
exports.UltraLowLatencySignalingService = UltraLowLatencySignalingService = UltraLowLatencySignalingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [session_service_1.SessionService,
        performance_service_1.PerformanceService,
        metrics_service_1.MetricsService])
], UltraLowLatencySignalingService);
class LatencyMonitor {
    constructor() {
        this.latencies = [];
        this.signalingLatencies = [];
    }
    recordSignalingLatency(latency) {
        this.signalingLatencies.push(latency);
        if (this.signalingLatencies.length > 1000) {
            this.signalingLatencies.shift();
        }
    }
    getAverageLatency() {
        if (this.latencies.length === 0)
            return 0;
        return this.latencies.reduce((sum, lat) => sum + lat, 0) / this.latencies.length;
    }
    getSignalingLatency() {
        if (this.signalingLatencies.length === 0)
            return 0;
        return this.signalingLatencies.reduce((sum, lat) => sum + lat, 0) / this.signalingLatencies.length;
    }
    getStatistics() {
        return {
            averageLatency: this.getAverageLatency(),
            signalingLatency: this.getSignalingLatency(),
            sampleCount: this.latencies.length
        };
    }
}
