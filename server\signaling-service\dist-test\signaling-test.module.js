"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SignalingTestModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const ultra_low_latency_signaling_service_1 = require("./ultra-low-latency-signaling.service");
const signaling_controller_1 = require("./controllers/signaling.controller");
const monitoring_controller_1 = require("./controllers/monitoring.controller");
const signaling_gateway_1 = require("./gateways/signaling.gateway");
const performance_service_1 = require("./services/performance.service");
const session_service_1 = require("./services/session.service");
const metrics_service_1 = require("./services/metrics.service");
// 简化的会话服务，不依赖Redis
class SimpleSessionService {
    constructor() {
        this.sessions = new Map();
    }
    async createSession(createSessionDto) {
        const sessionId = Math.random().toString(36).substring(2, 15);
        const session = {
            id: sessionId,
            name: createSessionDto.name,
            description: createSessionDto.description,
            maxParticipants: createSessionDto.maxParticipants || 10,
            type: createSessionDto.type || 'webrtc',
            participants: [],
            createdAt: new Date(),
            updatedAt: new Date(),
            config: createSessionDto.config || {},
        };
        this.sessions.set(sessionId, session);
        return session;
    }
    async getSession(sessionId) {
        return this.sessions.get(sessionId) || null;
    }
    async joinSession(sessionId, joinSessionDto) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new Error('会话不存在');
        }
        if (session.participants.length >= session.maxParticipants) {
            throw new Error('会话已满');
        }
        if (session.participants.includes(joinSessionDto.peerId)) {
            throw new Error('已在会话中');
        }
        session.participants.push(joinSessionDto.peerId);
        session.updatedAt = new Date();
        return {
            sessionId,
            participants: session.participants,
            capabilities: joinSessionDto.capabilities,
        };
    }
    async leaveSession(sessionId, peerId) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            return;
        }
        const index = session.participants.indexOf(peerId);
        if (index > -1) {
            session.participants.splice(index, 1);
            session.updatedAt = new Date();
        }
    }
    async getActiveSessions(page = 1, limit = 10) {
        const sessions = Array.from(this.sessions.values());
        return {
            sessions: sessions.slice((page - 1) * limit, page * limit),
            total: sessions.length,
            page,
            limit,
            totalPages: Math.ceil(sessions.length / limit),
        };
    }
}
// 简化的指标服务
class SimpleMetricsService {
    recordLatency(latency) {
        // 简化实现
    }
    recordThroughput(messages, bytes) {
        // 简化实现
    }
    async getLatencyStats(periodMinutes = 60) {
        return {
            average: 10,
            min: 5,
            max: 20,
            p50: 10,
            p95: 18,
            p99: 20,
            samples: 100,
            period: periodMinutes,
        };
    }
    async getThroughputStats(intervalSeconds = 60) {
        return {
            messagesPerSecond: 50,
            bytesPerSecond: 1024,
            peakMessagesPerSecond: 100,
            peakBytesPerSecond: 2048,
            interval: intervalSeconds,
            timestamp: new Date(),
        };
    }
}
let SignalingTestModule = class SignalingTestModule {
};
exports.SignalingTestModule = SignalingTestModule;
exports.SignalingTestModule = SignalingTestModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ['.env.test', '.env'],
            }),
        ],
        controllers: [
            signaling_controller_1.SignalingController,
            monitoring_controller_1.MonitoringController,
        ],
        providers: [
            ultra_low_latency_signaling_service_1.UltraLowLatencySignalingService,
            signaling_gateway_1.SignalingGateway,
            performance_service_1.PerformanceService,
            {
                provide: session_service_1.SessionService,
                useClass: SimpleSessionService,
            },
            {
                provide: metrics_service_1.MetricsService,
                useClass: SimpleMetricsService,
            },
        ],
        exports: [
            ultra_low_latency_signaling_service_1.UltraLowLatencySignalingService,
            performance_service_1.PerformanceService,
            session_service_1.SessionService,
            metrics_service_1.MetricsService,
        ],
    })
], SignalingTestModule);
