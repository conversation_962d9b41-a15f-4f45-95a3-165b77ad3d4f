# 空间信息系统服务 (Spatial Service)

基于NestJS构建的企业级空间信息系统服务，提供完整的GIS功能和空间数据管理能力。

## 🌟 功能特性

### 核心功能
- 🗺️ **空间数据管理**: 支持多种空间数据格式的导入、存储和管理
- 🔍 **空间查询**: 强大的空间查询和检索功能
- 📊 **空间分析**: 缓冲区分析、叠加分析、网络分析等高级空间分析
- 🎯 **地图服务**: WMS/WFS标准服务、瓦片服务、地理编码等
- 📈 **批量处理**: 大数据量的批量导入导出和处理
- 🔐 **权限管理**: 基于JWT的用户认证和权限控制

### 技术特性
- ⚡ **高性能**: 基于PostGIS的空间索引和优化查询
- 🔄 **实时处理**: 支持实时空间数据更新和推送
- 📦 **容器化**: 完整的Docker容器化部署方案
- 📊 **监控完善**: 健康检查、性能监控和告警系统
- 🔧 **可扩展**: 模块化架构，易于扩展和定制

## 🏗️ 技术架构

### 技术栈
- **后端框架**: NestJS + TypeScript
- **数据库**: PostgreSQL + PostGIS
- **缓存**: Redis
- **队列**: Bull (Redis-based)
- **空间库**: Turf.js, GDAL, GEOS, Proj4
- **容器**: Docker + Docker Compose
- **监控**: Prometheus + Grafana

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │  Mobile Client  │    │  Third Party    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      Nginx (Proxy)       │
                    └─────────────┬─────────────┘
                                  │
                    ┌─────────────┴─────────────┐
                    │    Spatial Service       │
                    │   (NestJS + TypeScript)  │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
┌─────────┴─────────┐   ┌─────────┴─────────┐   ┌─────────┴─────────┐
│ PostgreSQL+PostGIS│   │      Redis        │   │   External APIs   │
│   (Spatial DB)    │   │   (Cache/Queue)   │   │ (Geocoding/Weather)│
└───────────────────┘   └───────────────────┘   └───────────────────┘
```

## 🚀 快速开始

### 环境要求
- Node.js 18+
- PostgreSQL 13+ (带PostGIS扩展)
- Redis 6+
- Docker & Docker Compose (推荐)

### 使用Docker部署 (推荐)

1. **克隆项目**
```bash
git clone <repository-url>
cd server/spatial-service
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件配置数据库等参数
```

3. **启动服务**
```bash
docker-compose up -d
```

4. **验证部署**
```bash
# 检查服务状态
docker-compose ps

# 查看健康状态
curl http://localhost:3001/health
```

### 本地开发部署

1. **安装依赖**
```bash
npm install
```

2. **配置数据库**
```bash
# 创建PostgreSQL数据库
createdb spatial_db

# 启用PostGIS扩展
psql -d spatial_db -c "CREATE EXTENSION postgis;"
```

3. **启动Redis**
```bash
redis-server
```

4. **运行开发服务器**
```bash
npm run start:dev
```

## 📚 API文档

### 访问地址
- **Swagger文档**: http://localhost:3001/api
- **健康检查**: http://localhost:3001/health
- **监控面板**: http://localhost:3000 (Grafana)

### 主要API端点

#### 空间数据管理
```
GET    /api/v1/spatial-data/projects          # 获取项目列表
POST   /api/v1/spatial-data/projects          # 创建项目
GET    /api/v1/spatial-data/layers            # 获取图层列表
POST   /api/v1/spatial-data/layers            # 创建图层
GET    /api/v1/spatial-data/features          # 获取要素列表
POST   /api/v1/spatial-data/features          # 创建要素
```

#### 空间分析
```
POST   /spatial-analysis/buffer               # 缓冲区分析
POST   /spatial-analysis/overlay              # 叠加分析
POST   /spatial-analysis/network              # 网络分析
POST   /spatial-analysis/statistical          # 统计分析
```

#### 地图服务
```
GET    /map-services/tiles/{z}/{x}/{y}        # 瓦片服务
GET    /map-services/wms                      # WMS服务
GET    /map-services/wfs                      # WFS服务
GET    /map-services/geocoding/search         # 地理编码
```

#### 批量处理
```
POST   /batch-processing/import               # 批量导入
POST   /batch-processing/export               # 批量导出
GET    /batch-processing/jobs/{id}            # 查询任务状态
```

## 🔧 配置说明

### 环境变量配置

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `NODE_ENV` | 运行环境 | development |
| `PORT` | 服务端口 | 3001 |
| `DB_HOST` | 数据库主机 | localhost |
| `DB_PORT` | 数据库端口 | 5432 |
| `DB_USERNAME` | 数据库用户名 | postgres |
| `DB_PASSWORD` | 数据库密码 | password |
| `DB_DATABASE` | 数据库名称 | spatial_db |
| `REDIS_HOST` | Redis主机 | localhost |
| `REDIS_PORT` | Redis端口 | 6379 |
| `JWT_SECRET` | JWT密钥 | 需要设置 |

### 数据库配置

#### PostGIS扩展
```sql
-- 启用PostGIS扩展
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;
CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;
CREATE EXTENSION IF NOT EXISTS postgis_tiger_geocoder;

-- 验证安装
SELECT PostGIS_Version();
```

#### 空间索引优化
```sql
-- 为几何字段创建空间索引
CREATE INDEX idx_spatial_features_geom 
ON spatial_features USING GIST (geometry);

-- 创建属性索引
CREATE INDEX idx_spatial_features_layer_id 
ON spatial_features (layer_id);
```

## 📊 监控和运维

### 健康检查
```bash
# 基础健康检查
curl http://localhost:3001/health

# 详细健康检查
curl http://localhost:3001/health/detailed

# 空间服务检查
curl http://localhost:3001/health/spatial

# 数据库检查
curl http://localhost:3001/health/database
```

### 性能监控
- **Prometheus**: http://localhost:9091
- **Grafana**: http://localhost:3000 (admin/admin)
- **Redis Commander**: http://localhost:8081

### 日志管理
```bash
# 查看应用日志
docker-compose logs -f spatial-service

# 查看数据库日志
docker-compose logs -f postgres

# 查看所有服务日志
docker-compose logs -f
```

## 🧪 测试

### 运行测试
```bash
# 单元测试
npm run test

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

### API测试
```bash
# 使用curl测试
curl -X GET http://localhost:3001/health

# 使用Postman导入
# 导入文件: docs/postman/spatial-service.json
```

## 🔒 安全配置

### 认证和授权
- JWT Token认证
- 基于角色的权限控制
- API访问频率限制

### 数据安全
- 数据库连接加密
- 敏感数据脱敏
- 审计日志记录

## 📈 性能优化

### 数据库优化
- 空间索引优化
- 查询语句优化
- 连接池配置

### 缓存策略
- Redis查询缓存
- 瓦片缓存
- 分析结果缓存

### 并发处理
- 队列任务处理
- 批量操作优化
- 资源限制控制

## 🚀 部署指南

### 生产环境部署
1. 配置生产环境变量
2. 设置SSL证书
3. 配置负载均衡
4. 设置监控告警
5. 配置备份策略

### 扩展部署
- 水平扩展: 多实例部署
- 数据库分片: 按地理区域分片
- CDN加速: 瓦片服务CDN

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码变更
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 📞 支持

- 文档: [项目Wiki](wiki-url)
- 问题反馈: [Issues](issues-url)
- 技术支持: <EMAIL>
