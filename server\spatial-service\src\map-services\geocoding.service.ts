/**
 * 地理编码服务
 */
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class GeocodingService {
  private readonly logger = new Logger(GeocodingService.name);

  async search(query: string, limit: number = 10, bbox?: string) {
    this.logger.log(`地理编码搜索: ${query}`);
    
    // 简化实现，返回模拟数据
    return {
      success: true,
      results: [
        {
          display_name: `搜索结果: ${query}`,
          lat: 39.9042,
          lon: 116.4074,
          boundingbox: ['39.9', '39.91', '116.4', '116.41'],
          class: 'place',
          type: 'city'
        }
      ],
      query,
      limit
    };
  }

  async reverse(lat: number, lon: number) {
    this.logger.log(`反向地理编码: ${lat}, ${lon}`);
    
    // 简化实现，返回模拟数据
    return {
      success: true,
      result: {
        display_name: `位置 ${lat}, ${lon}`,
        lat,
        lon,
        address: {
          country: '中国',
          state: '北京市',
          city: '北京市',
          road: '示例路'
        }
      }
    };
  }
}
