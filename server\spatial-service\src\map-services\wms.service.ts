/**
 * WMS服务
 */
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class WmsService {
  private readonly logger = new Logger(WmsService.name);

  async handleRequest(query: any) {
    this.logger.log(`WMS请求: ${JSON.stringify(query)}`);
    
    const request = query.REQUEST?.toUpperCase();
    
    switch (request) {
      case 'GETCAPABILITIES':
        return this.getCapabilities();
      case 'GETMAP':
        return this.getMap(query);
      case 'GETFEATUREINFO':
        return this.getFeatureInfo(query);
      default:
        return {
          contentType: 'application/xml',
          data: this.createErrorResponse('InvalidRequest', '不支持的请求类型')
        };
    }
  }

  private getCapabilities() {
    const capabilities = `<?xml version="1.0" encoding="UTF-8"?>
<WMS_Capabilities version="1.3.0">
  <Service>
    <Name>WMS</Name>
    <Title>Spatial Service WMS</Title>
    <Abstract>空间信息系统WMS服务</Abstract>
  </Service>
  <Capability>
    <Request>
      <GetCapabilities>
        <Format>text/xml</Format>
      </GetCapabilities>
      <GetMap>
        <Format>image/png</Format>
        <Format>image/jpeg</Format>
      </GetMap>
    </Request>
  </Capability>
</WMS_Capabilities>`;

    return {
      contentType: 'application/xml',
      data: capabilities
    };
  }

  private getMap(query: any) {
    // 简化实现，返回空白图片
    return {
      contentType: 'image/png',
      data: Buffer.alloc(0)
    };
  }

  private getFeatureInfo(query: any) {
    return {
      contentType: 'application/json',
      data: JSON.stringify({ features: [] })
    };
  }

  private createErrorResponse(code: string, message: string) {
    return `<?xml version="1.0" encoding="UTF-8"?>
<ServiceExceptionReport version="1.3.0">
  <ServiceException code="${code}">${message}</ServiceException>
</ServiceExceptionReport>`;
  }
}
