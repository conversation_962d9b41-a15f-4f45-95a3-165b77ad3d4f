"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitoringController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const ultra_low_latency_signaling_service_1 = require("../ultra-low-latency-signaling.service");
const performance_service_1 = require("../services/performance.service");
const metrics_service_1 = require("../services/metrics.service");
let MonitoringController = class MonitoringController {
    constructor(signalingService, performanceService, metricsService) {
        this.signalingService = signalingService;
        this.performanceService = performanceService;
        this.metricsService = metricsService;
    }
    getMetrics() {
        const metrics = this.signalingService.getPerformanceMetrics();
        const systemMetrics = this.performanceService.getSystemMetrics();
        return {
            success: true,
            data: {
                signaling: metrics,
                system: systemMetrics,
                timestamp: new Date().toISOString()
            }
        };
    }
    async getLatencyStats(period = 60) {
        const stats = await this.metricsService.getLatencyStats(period);
        return {
            success: true,
            data: stats
        };
    }
    getConnectionStats() {
        const stats = this.performanceService.getConnectionStats();
        return {
            success: true,
            data: stats
        };
    }
    async getThroughputStats(interval = 60) {
        const stats = await this.metricsService.getThroughputStats(interval);
        return {
            success: true,
            data: stats
        };
    }
    async getAlerts() {
        const alerts = await this.performanceService.getActiveAlerts();
        return {
            success: true,
            data: alerts
        };
    }
    getServiceStatus() {
        const status = this.performanceService.getServiceStatus();
        return {
            success: true,
            data: status
        };
    }
};
exports.MonitoringController = MonitoringController;
__decorate([
    (0, common_1.Get)('metrics'),
    (0, swagger_1.ApiOperation)({ summary: '获取性能指标' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取指标成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], MonitoringController.prototype, "getMetrics", null);
__decorate([
    (0, common_1.Get)('latency'),
    (0, swagger_1.ApiOperation)({ summary: '获取延迟统计' }),
    (0, swagger_1.ApiQuery)({ name: 'period', required: false, description: '统计周期(minutes)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取延迟统计成功' }),
    __param(0, (0, common_1.Query)('period')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MonitoringController.prototype, "getLatencyStats", null);
__decorate([
    (0, common_1.Get)('connections'),
    (0, swagger_1.ApiOperation)({ summary: '获取连接统计' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取连接统计成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], MonitoringController.prototype, "getConnectionStats", null);
__decorate([
    (0, common_1.Get)('throughput'),
    (0, swagger_1.ApiOperation)({ summary: '获取吞吐量统计' }),
    (0, swagger_1.ApiQuery)({ name: 'interval', required: false, description: '统计间隔(seconds)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取吞吐量统计成功' }),
    __param(0, (0, common_1.Query)('interval')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MonitoringController.prototype, "getThroughputStats", null);
__decorate([
    (0, common_1.Get)('alerts'),
    (0, swagger_1.ApiOperation)({ summary: '获取系统告警' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取告警信息成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MonitoringController.prototype, "getAlerts", null);
__decorate([
    (0, common_1.Get)('status'),
    (0, swagger_1.ApiOperation)({ summary: '获取服务状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取服务状态成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], MonitoringController.prototype, "getServiceStatus", null);
exports.MonitoringController = MonitoringController = __decorate([
    (0, swagger_1.ApiTags)('monitoring'),
    (0, common_1.Controller)('monitoring'),
    __metadata("design:paramtypes", [ultra_low_latency_signaling_service_1.UltraLowLatencySignalingService,
        performance_service_1.PerformanceService,
        metrics_service_1.MetricsService])
], MonitoringController);
