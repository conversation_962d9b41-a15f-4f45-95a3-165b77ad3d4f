"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SignalingGateway_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SignalingGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const common_1 = require("@nestjs/common");
const socket_io_1 = require("socket.io");
const ultra_low_latency_signaling_service_1 = require("../ultra-low-latency-signaling.service");
let SignalingGateway = SignalingGateway_1 = class SignalingGateway {
    constructor(signalingService) {
        this.signalingService = signalingService;
        this.logger = new common_1.Logger(SignalingGateway_1.name);
    }
    afterInit(server) {
        this.logger.log('WebSocket网关已初始化');
        // 将服务器实例传递给信令服务
        this.signalingService.setServer(server);
    }
    async handleConnection(client) {
        this.logger.log(`客户端连接: ${client.id}`);
        await this.signalingService.handleConnection(client);
    }
    handleDisconnect(client) {
        this.logger.log(`客户端断开: ${client.id}`);
        this.signalingService.handleDisconnection(client.id);
    }
    async handleJoinSession(client, data) {
        return await this.signalingService.handleJoinSession(client, data);
    }
    async handleLeaveSession(client, data) {
        return await this.signalingService.handleLeaveSession(client, data);
    }
    async handleSignal(client, message) {
        return await this.signalingService.handleSignal(client, message);
    }
    handlePing(client, timestamp) {
        return this.signalingService.handlePing(client, timestamp);
    }
    handleCapabilityResponse(client, capabilities) {
        return this.signalingService.handleCapabilityResponse(client, capabilities);
    }
};
exports.SignalingGateway = SignalingGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], SignalingGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('join-session'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], SignalingGateway.prototype, "handleJoinSession", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('leave-session'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], SignalingGateway.prototype, "handleLeaveSession", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('signal'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], SignalingGateway.prototype, "handleSignal", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('ping'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Number]),
    __metadata("design:returntype", void 0)
], SignalingGateway.prototype, "handlePing", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('capability-response'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", void 0)
], SignalingGateway.prototype, "handleCapabilityResponse", null);
exports.SignalingGateway = SignalingGateway = SignalingGateway_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: '*',
            credentials: true,
        },
        transports: ['websocket'],
        namespace: '/signaling',
    }),
    __metadata("design:paramtypes", [ultra_low_latency_signaling_service_1.UltraLowLatencySignalingService])
], SignalingGateway);
