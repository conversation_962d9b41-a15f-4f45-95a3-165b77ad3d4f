/**
 * 认证集成安全防护体系服务启动文件
 * 支持完整身份认证与授权的安全服务
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AuthIntegratedAppModule } from './auth-integrated-app.module';

async function bootstrap() {
  const logger = new Logger('AuthIntegratedSecurityService');
  
  try {
    console.log('🚀 开始启动认证集成安全防护体系服务...');
    
    // 创建应用实例
    const app = await NestFactory.create(AuthIntegratedAppModule, {
      logger: ['log', 'error', 'warn', 'debug'],
    });

    console.log('✅ 应用创建成功');

    // 获取配置服务
    const configService = app.get(ConfigService);

    // 设置全局前缀
    const globalPrefix = 'api/v1';
    app.setGlobalPrefix(globalPrefix);

    // 启用全局验证管道
    app.useGlobalPipes(new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      disableErrorMessages: false,
    }));

    // 启用CORS
    app.enableCors({
      origin: '*',
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    });

    console.log('✅ 基础配置完成');

    // 配置Swagger文档
    const swaggerConfig = new DocumentBuilder()
      .setTitle('认证集成安全防护体系服务API')
      .setDescription(`
        DL引擎认证集成安全防护体系服务API文档
        
        🔐 身份认证与授权：
        - JWT令牌认证
        - 角色权限控制
        - 多因素认证(MFA)
        - 会话管理
        - 用户管理
        - 密码策略
        - 账户锁定保护
        
        🛡️ 安全功能：
        - 零信任安全架构
        - 威胁检测与响应
        - 安全策略管理
        - 风险评估与监控
        - 安全事件管理
        - 合规性检查
        
        💾 数据管理：
        - 高性能内存存储
        - 实时数据处理
        - 安全审计日志
        - 访问记录追踪
        
        🔧 管理功能：
        - 用户生命周期管理
        - 权限分配管理
        - 安全策略配置
        - 系统监控告警
      `)
      .setVersion('3.0.0')
      .addTag('auth', '🔐 身份认证')
      .addTag('users', '👥 用户管理')
      .addTag('security', '🛡️ 安全管理')
      .addTag('zero-trust', '🔒 零信任架构')
      .addTag('threat-detection', '🔍 威胁检测')
      .addTag('monitoring', '📊 安全监控')
      .addTag('compliance', '📋 合规检查')
      .addTag('audit', '📝 安全审计')
      .addBearerAuth()
      .addApiKey({ type: 'apiKey', name: 'X-API-Key', in: 'header' }, 'api-key')
      .build();

    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup(`${globalPrefix}/docs`, app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
        docExpansion: 'none',
        filter: true,
        showRequestDuration: true,
      },
      customSiteTitle: '认证集成安全防护体系服务API文档',
      customfavIcon: '/favicon.ico',
    });

    console.log('✅ Swagger配置完成');

    // 启动HTTP服务
    const port = 13000;
    const host = '127.0.0.1';

    await app.listen(port, host);

    logger.log(`🚀 认证集成安全防护体系服务已启动`);
    logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
    logger.log(`🌐 全局前缀: /${globalPrefix}`);
    logger.log(`📚 API文档: http://${host}:${port}/${globalPrefix}/docs`);
    logger.log(`📊 健康检查: http://${host}:${port}/${globalPrefix}/health`);
    logger.log(`🔐 用户认证: http://${host}:${port}/${globalPrefix}/auth`);
    logger.log(`👥 用户管理: http://${host}:${port}/${globalPrefix}/auth/users`);
    logger.log(`🛡️ 安全管理: http://${host}:${port}/${globalPrefix}/security`);
    logger.log(`🔒 零信任认证: http://${host}:${port}/${globalPrefix}/security/zero-trust`);
    logger.log(`🔍 威胁检测: http://${host}:${port}/${globalPrefix}/security/threat-detection`);
    logger.log(`🔧 环境: ${configService.get<string>('NODE_ENV', 'development')}`);
    logger.log(`💾 存储模式: 内存存储（高性能模式）`);

    // 输出认证功能特性
    console.log('\n🔐 身份认证与授权特性:');
    console.log('  ✅ JWT令牌认证');
    console.log('  ✅ 角色权限控制');
    console.log('  ✅ 多因素认证(MFA)');
    console.log('  ✅ 会话管理');
    console.log('  ✅ 用户生命周期管理');
    console.log('  ✅ 密码策略控制');
    console.log('  ✅ 账户锁定保护');
    console.log('  ✅ 访问审计日志');

    // 输出安全功能特性
    console.log('\n🛡️ 安全防护特性:');
    console.log('  ✅ 零信任安全架构');
    console.log('  ✅ 实时威胁检测');
    console.log('  ✅ 安全策略管理');
    console.log('  ✅ 风险评估分析');
    console.log('  ✅ 安全事件监控');
    console.log('  ✅ 设备信任评估');
    console.log('  ✅ 合规性检查');
    console.log('  ✅ 安全指标统计');

    // 输出默认用户信息
    console.log('\n👤 默认用户账户:');
    console.log('  🔑 管理员: admin / admin123');
    console.log('  🔍 安全分析师: analyst / analyst123');
    console.log('  📝 注册新用户: POST /api/v1/auth/register');

    console.log('\n💡 性能优势:');
    console.log('  🚀 毫秒级认证响应');
    console.log('  📈 高并发用户支持');
    console.log('  💾 零数据库依赖');
    console.log('  🔄 实时权限验证');
    console.log('  ⚡ 极速服务启动');

  } catch (error) {
    logger.error('服务启动失败:', error);
    console.error('详细错误:', error);
    process.exit(1);
  }
}

// 优雅关闭处理
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在优雅关闭...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在优雅关闭...');
  process.exit(0);
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason, 'at:', promise);
  process.exit(1);
});

bootstrap();
