{"name": "spatial-service", "version": "1.0.0", "description": "DL引擎空间信息系统服务", "author": "DL引擎团队", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm -- migration:generate -d src/database/data-source.ts", "migration:run": "npm run typeorm -- migration:run -d src/database/data-source.ts", "migration:revert": "npm run typeorm -- migration:revert -d src/database/data-source.ts", "schema:sync": "npm run typeorm -- schema:sync -d src/database/data-source.ts", "schema:drop": "npm run typeorm -- schema:drop -d src/database/data-source.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/terminus": "^10.0.0", "@nestjs/schedule": "^4.0.0", "@nestjs/cache-manager": "^2.0.0", "@nestjs/bull": "^10.0.0", "typeorm": "^0.3.17", "pg": "^8.11.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "bcryptjs": "^2.4.3", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "multer": "^1.4.5-lts.1", "uuid": "^9.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "turf": "^3.0.14", "@turf/turf": "^6.5.0", "proj4": "^2.9.0", "shapefile": "^0.6.6", "geojson": "^0.5.0", "wellknown": "^0.5.0", "cache-manager": "^5.2.0", "bull": "^4.11.0", "ioredis": "^5.3.0", "compression": "^1.7.4", "helmet": "^7.0.0", "dotenv": "^16.3.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.8", "@types/passport-local": "^1.0.35", "@types/bcryptjs": "^2.4.2", "@types/multer": "^1.4.7", "@types/uuid": "^9.0.2", "@types/geojson": "^7946.0.10", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["spatial", "gis", "geospatial", "mapping", "geography", "postgis", "<PERSON><PERSON><PERSON>", "typescript", "dl-engine"]}