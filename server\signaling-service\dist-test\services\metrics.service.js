"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MetricsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetricsService = void 0;
const common_1 = require("@nestjs/common");
const bull_1 = require("@nestjs/bull");
const common_2 = require("@nestjs/common");
const cache_manager_1 = require("@nestjs/cache-manager");
let MetricsService = MetricsService_1 = class MetricsService {
    constructor(metricsQueue, cacheManager) {
        this.metricsQueue = metricsQueue;
        this.cacheManager = cacheManager;
        this.logger = new common_1.Logger(MetricsService_1.name);
        this.latencyBuffer = [];
        this.throughputBuffer = [];
        this.initializeMetricsCollection();
    }
    initializeMetricsCollection() {
        // 定期清理缓冲区
        setInterval(() => {
            this.cleanupBuffers();
        }, 60000); // 每分钟清理一次
        // 定期计算和存储指标
        setInterval(() => {
            this.calculateAndStoreMetrics();
        }, 10000); // 每10秒计算一次
    }
    recordLatency(latency) {
        this.latencyBuffer.push(latency);
        // 限制缓冲区大小
        if (this.latencyBuffer.length > 10000) {
            this.latencyBuffer = this.latencyBuffer.slice(-5000);
        }
    }
    recordThroughput(messages, bytes) {
        this.throughputBuffer.push({
            timestamp: new Date(),
            messages,
            bytes,
        });
        // 限制缓冲区大小
        if (this.throughputBuffer.length > 1000) {
            this.throughputBuffer = this.throughputBuffer.slice(-500);
        }
    }
    async getLatencyStats(periodMinutes = 60) {
        const cacheKey = `latency_stats_${periodMinutes}`;
        const cached = await this.cacheManager.get(cacheKey);
        if (cached) {
            return cached;
        }
        const cutoffTime = Date.now() - (periodMinutes * 60 * 1000);
        const recentLatencies = this.latencyBuffer.filter(l => l > 0);
        if (recentLatencies.length === 0) {
            return {
                average: 0,
                min: 0,
                max: 0,
                p50: 0,
                p95: 0,
                p99: 0,
                samples: 0,
                period: periodMinutes,
            };
        }
        const sorted = recentLatencies.sort((a, b) => a - b);
        const stats = {
            average: sorted.reduce((sum, val) => sum + val, 0) / sorted.length,
            min: sorted[0],
            max: sorted[sorted.length - 1],
            p50: this.percentile(sorted, 0.5),
            p95: this.percentile(sorted, 0.95),
            p99: this.percentile(sorted, 0.99),
            samples: sorted.length,
            period: periodMinutes,
        };
        // 缓存结果
        await this.cacheManager.set(cacheKey, stats, 30000); // 30秒缓存
        return stats;
    }
    async getThroughputStats(intervalSeconds = 60) {
        const cacheKey = `throughput_stats_${intervalSeconds}`;
        const cached = await this.cacheManager.get(cacheKey);
        if (cached) {
            return cached;
        }
        const cutoffTime = new Date(Date.now() - (intervalSeconds * 1000));
        const recentData = this.throughputBuffer.filter(d => d.timestamp >= cutoffTime);
        if (recentData.length === 0) {
            return {
                messagesPerSecond: 0,
                bytesPerSecond: 0,
                peakMessagesPerSecond: 0,
                peakBytesPerSecond: 0,
                interval: intervalSeconds,
                timestamp: new Date(),
            };
        }
        const totalMessages = recentData.reduce((sum, d) => sum + d.messages, 0);
        const totalBytes = recentData.reduce((sum, d) => sum + d.bytes, 0);
        // 计算峰值（每秒）
        const secondlyData = this.groupBySecond(recentData);
        const peakMessages = Math.max(...secondlyData.map(d => d.messages));
        const peakBytes = Math.max(...secondlyData.map(d => d.bytes));
        const stats = {
            messagesPerSecond: totalMessages / intervalSeconds,
            bytesPerSecond: totalBytes / intervalSeconds,
            peakMessagesPerSecond: peakMessages,
            peakBytesPerSecond: peakBytes,
            interval: intervalSeconds,
            timestamp: new Date(),
        };
        // 缓存结果
        await this.cacheManager.set(cacheKey, stats, 10000); // 10秒缓存
        return stats;
    }
    percentile(sorted, p) {
        const index = Math.ceil(sorted.length * p) - 1;
        return sorted[Math.max(0, index)];
    }
    groupBySecond(data) {
        const grouped = new Map();
        data.forEach(item => {
            const second = Math.floor(item.timestamp.getTime() / 1000);
            const existing = grouped.get(second) || { messages: 0, bytes: 0 };
            existing.messages += item.messages;
            existing.bytes += item.bytes;
            grouped.set(second, existing);
        });
        return Array.from(grouped.values());
    }
    cleanupBuffers() {
        const cutoffTime = Date.now() - (3600 * 1000); // 保留1小时数据
        // 清理延迟缓冲区（保留最近的数据）
        if (this.latencyBuffer.length > 5000) {
            this.latencyBuffer = this.latencyBuffer.slice(-2500);
        }
        // 清理吞吐量缓冲区
        this.throughputBuffer = this.throughputBuffer.filter(item => item.timestamp.getTime() > cutoffTime);
        this.logger.debug(`缓冲区清理完成 - 延迟样本: ${this.latencyBuffer.length}, 吞吐量样本: ${this.throughputBuffer.length}`);
    }
    async calculateAndStoreMetrics() {
        try {
            // 计算当前指标
            const latencyStats = await this.getLatencyStats(10); // 10分钟
            const throughputStats = await this.getThroughputStats(60); // 1分钟
            // 将指标添加到队列进行异步处理
            await this.metricsQueue.add('store-metrics', {
                latency: latencyStats,
                throughput: throughputStats,
                timestamp: new Date(),
            });
        }
        catch (error) {
            this.logger.error('计算指标失败:', error);
        }
    }
    async getHistoricalMetrics(hours = 24) {
        // 这里应该从持久化存储中获取历史指标
        // 简化实现，返回模拟数据
        return {
            period: hours,
            data: [],
            message: '历史指标功能需要配置持久化存储'
        };
    }
};
exports.MetricsService = MetricsService;
exports.MetricsService = MetricsService = MetricsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, bull_1.InjectQueue)('signaling-metrics')),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [Object, Object])
], MetricsService);
