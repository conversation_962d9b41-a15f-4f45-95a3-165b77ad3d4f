import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { UltraLowLatencySignalingService } from './ultra-low-latency-signaling.service';
import { SignalingController } from './controllers/signaling.controller';
import { MonitoringController } from './controllers/monitoring.controller';
import { SignalingGateway } from './gateways/signaling.gateway';
import { PerformanceService } from './services/performance.service';
import { SessionService } from './services/session.service';
import { MetricsService } from './services/metrics.service';

// 简化的会话服务，不依赖Redis
class SimpleSessionService {
  private sessions = new Map();

  async createSession(createSessionDto: any) {
    const sessionId = Math.random().toString(36).substring(2, 15);
    const session = {
      id: sessionId,
      name: createSessionDto.name,
      description: createSessionDto.description,
      maxParticipants: createSessionDto.maxParticipants || 10,
      type: createSessionDto.type || 'webrtc',
      participants: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      config: createSessionDto.config || {},
    };

    this.sessions.set(sessionId, session);
    return session;
  }

  async getSession(sessionId: string) {
    return this.sessions.get(sessionId) || null;
  }

  async joinSession(sessionId: string, joinSessionDto: any) {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error('会话不存在');
    }

    if (session.participants.length >= session.maxParticipants) {
      throw new Error('会话已满');
    }

    if (session.participants.includes(joinSessionDto.peerId)) {
      throw new Error('已在会话中');
    }

    session.participants.push(joinSessionDto.peerId);
    session.updatedAt = new Date();

    return {
      sessionId,
      participants: session.participants,
      capabilities: joinSessionDto.capabilities,
    };
  }

  async leaveSession(sessionId: string, peerId: string) {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return;
    }

    const index = session.participants.indexOf(peerId);
    if (index > -1) {
      session.participants.splice(index, 1);
      session.updatedAt = new Date();
    }
  }

  async getActiveSessions(page: number = 1, limit: number = 10) {
    const sessions = Array.from(this.sessions.values());
    return {
      sessions: sessions.slice((page - 1) * limit, page * limit),
      total: sessions.length,
      page,
      limit,
      totalPages: Math.ceil(sessions.length / limit),
    };
  }
}

// 简化的指标服务
class SimpleMetricsService {
  recordLatency(latency: number) {
    // 简化实现
  }

  recordThroughput(messages: number, bytes: number) {
    // 简化实现
  }

  async getLatencyStats(periodMinutes: number = 60) {
    return {
      average: 10,
      min: 5,
      max: 20,
      p50: 10,
      p95: 18,
      p99: 20,
      samples: 100,
      period: periodMinutes,
    };
  }

  async getThroughputStats(intervalSeconds: number = 60) {
    return {
      messagesPerSecond: 50,
      bytesPerSecond: 1024,
      peakMessagesPerSecond: 100,
      peakBytesPerSecond: 2048,
      interval: intervalSeconds,
      timestamp: new Date(),
    };
  }
}

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.test', '.env'],
    }),
  ],
  controllers: [
    SignalingController,
    MonitoringController,
  ],
  providers: [
    UltraLowLatencySignalingService,
    SignalingGateway,
    PerformanceService,
    {
      provide: SessionService,
      useClass: SimpleSessionService,
    },
    {
      provide: MetricsService,
      useClass: SimpleMetricsService,
    },
  ],
  exports: [
    UltraLowLatencySignalingService,
    PerformanceService,
    SessionService,
    MetricsService,
  ],
})
export class SignalingTestModule {}
