/**
 * 地图服务控制器
 */
import { Controller, Get, Post, Query, Param, Body, Res, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TileService } from './tile.service';
import { WmsService } from './wms.service';
import { WfsService } from './wfs.service';
import { GeocodingService } from './geocoding.service';
import { ElevationService } from './elevation.service';
import { WeatherService } from './weather.service';

@ApiTags('地图服务')
@Controller('map-services')
export class MapServicesController {
  constructor(
    private readonly tileService: TileService,
    private readonly wmsService: WmsService,
    private readonly wfsService: WfsService,
    private readonly geocodingService: GeocodingService,
    private readonly elevationService: ElevationService,
    private readonly weatherService: WeatherService,
  ) {}

  @Get('tiles/:z/:x/:y')
  @ApiOperation({ summary: '获取地图瓦片' })
  @ApiResponse({ status: 200, description: '地图瓦片数据' })
  async getTile(
    @Param('z') z: number,
    @Param('x') x: number,
    @Param('y') y: number,
    @Query('layer') layer: string,
    @Res() res: Response
  ) {
    const tile = await this.tileService.getTile(z, x, y, layer);
    res.set('Content-Type', 'image/png');
    res.set('Cache-Control', 'public, max-age=3600');
    res.send(tile);
  }

  @Get('wms')
  @ApiOperation({ summary: 'WMS服务' })
  @ApiResponse({ status: 200, description: 'WMS响应' })
  @ApiQuery({ name: 'SERVICE', required: true })
  @ApiQuery({ name: 'REQUEST', required: true })
  @ApiQuery({ name: 'VERSION', required: false })
  async wmsService(
    @Query() query: any,
    @Res() res: Response
  ) {
    const result = await this.wmsService.handleRequest(query);
    
    if (result.contentType) {
      res.set('Content-Type', result.contentType);
    }
    
    res.send(result.data);
  }

  @Get('wfs')
  @ApiOperation({ summary: 'WFS服务' })
  @ApiResponse({ status: 200, description: 'WFS响应' })
  @ApiQuery({ name: 'SERVICE', required: true })
  @ApiQuery({ name: 'REQUEST', required: true })
  @ApiQuery({ name: 'VERSION', required: false })
  async wfsService(
    @Query() query: any,
    @Res() res: Response
  ) {
    const result = await this.wfsService.handleRequest(query);
    
    if (result.contentType) {
      res.set('Content-Type', result.contentType);
    }
    
    res.send(result.data);
  }

  @Get('geocoding/search')
  @ApiOperation({ summary: '地理编码搜索' })
  @ApiResponse({ status: 200, description: '地理编码结果' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async geocodingSearch(
    @Query('q') query: string,
    @Query('limit') limit: number = 10,
    @Query('bbox') bbox?: string
  ) {
    return this.geocodingService.search(query, limit, bbox);
  }

  @Get('geocoding/reverse')
  @ApiOperation({ summary: '反向地理编码' })
  @ApiResponse({ status: 200, description: '反向地理编码结果' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async reverseGeocoding(
    @Query('lat') lat: number,
    @Query('lon') lon: number
  ) {
    return this.geocodingService.reverse(lat, lon);
  }

  @Get('elevation')
  @ApiOperation({ summary: '获取高程数据' })
  @ApiResponse({ status: 200, description: '高程数据' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getElevation(
    @Query('lat') lat: number,
    @Query('lon') lon: number
  ) {
    return this.elevationService.getElevation(lat, lon);
  }

  @Post('elevation/batch')
  @ApiOperation({ summary: '批量获取高程数据' })
  @ApiResponse({ status: 200, description: '批量高程数据' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getBatchElevation(
    @Body() coordinates: { lat: number; lon: number }[]
  ) {
    return this.elevationService.getBatchElevation(coordinates);
  }

  @Get('weather')
  @ApiOperation({ summary: '获取天气数据' })
  @ApiResponse({ status: 200, description: '天气数据' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getWeather(
    @Query('lat') lat: number,
    @Query('lon') lon: number
  ) {
    return this.weatherService.getCurrentWeather(lat, lon);
  }

  @Get('weather/forecast')
  @ApiOperation({ summary: '获取天气预报' })
  @ApiResponse({ status: 200, description: '天气预报数据' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getWeatherForecast(
    @Query('lat') lat: number,
    @Query('lon') lon: number,
    @Query('days') days: number = 5
  ) {
    return this.weatherService.getForecast(lat, lon, days);
  }

  @Get('capabilities')
  @ApiOperation({ summary: '获取地图服务能力' })
  @ApiResponse({ status: 200, description: '服务能力列表' })
  async getCapabilities() {
    return {
      services: {
        tiles: {
          available: true,
          formats: ['png', 'jpg', 'webp'],
          maxZoom: 18,
          minZoom: 0
        },
        wms: {
          available: true,
          version: '1.3.0',
          formats: ['image/png', 'image/jpeg', 'application/json']
        },
        wfs: {
          available: true,
          version: '2.0.0',
          formats: ['application/json', 'application/gml+xml']
        },
        geocoding: {
          available: !!process.env.GEOCODING_API_KEY,
          provider: 'nominatim'
        },
        elevation: {
          available: !!process.env.ELEVATION_API_KEY,
          provider: 'open-elevation'
        },
        weather: {
          available: !!process.env.WEATHER_API_KEY,
          provider: 'openweathermap'
        }
      },
      coordinateSystems: [
        'EPSG:4326',
        'EPSG:3857',
        'EPSG:4490'
      ],
      outputFormats: [
        'application/json',
        'application/gml+xml',
        'image/png',
        'image/jpeg'
      ]
    };
  }
}
