/**
 * 瓦片服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SpatialFeature } from '../entities/spatial-feature.entity';
import { SpatialLayer } from '../entities/spatial-layer.entity';

@Injectable()
export class TileService {
  private readonly logger = new Logger(TileService.name);

  constructor(
    @InjectRepository(SpatialFeature)
    private spatialFeatureRepository: Repository<SpatialFeature>,
    @InjectRepository(SpatialLayer)
    private spatialLayerRepository: Repository<SpatialLayer>,
  ) {}

  /**
   * 获取地图瓦片
   */
  async getTile(z: number, x: number, y: number, layerId?: string): Promise<Buffer> {
    try {
      this.logger.log(`获取瓦片: z=${z}, x=${x}, y=${y}, layer=${layerId}`);

      // 计算瓦片边界
      const bbox = this.tileToBBox(x, y, z);
      
      if (layerId) {
        // 获取指定图层的瓦片
        return await this.generateLayerTile(layerId, bbox, z);
      } else {
        // 获取默认底图瓦片
        return await this.generateBaseTile(bbox, z);
      }

    } catch (error) {
      this.logger.error(`瓦片生成失败: ${error.message}`);
      // 返回空白瓦片
      return this.generateEmptyTile();
    }
  }

  /**
   * 计算瓦片边界框
   */
  private tileToBBox(x: number, y: number, z: number): [number, number, number, number] {
    const n = Math.pow(2, z);
    const lonMin = (x / n) * 360 - 180;
    const lonMax = ((x + 1) / n) * 360 - 180;
    const latMin = Math.atan(Math.sinh(Math.PI * (1 - 2 * (y + 1) / n))) * 180 / Math.PI;
    const latMax = Math.atan(Math.sinh(Math.PI * (1 - 2 * y / n))) * 180 / Math.PI;
    
    return [lonMin, latMin, lonMax, latMax];
  }

  /**
   * 生成图层瓦片
   */
  private async generateLayerTile(layerId: string, bbox: [number, number, number, number], zoom: number): Promise<Buffer> {
    // 查询瓦片范围内的要素
    const features = await this.spatialFeatureRepository
      .createQueryBuilder('feature')
      .where('feature.layerId = :layerId', { layerId })
      .andWhere(`
        ST_Intersects(
          feature.geometry::geometry,
          ST_MakeEnvelope(:minX, :minY, :maxX, :maxY, 4326)
        )
      `, {
        minX: bbox[0],
        minY: bbox[1],
        maxX: bbox[2],
        maxY: bbox[3]
      })
      .getMany();

    // 获取图层样式
    const layer = await this.spatialLayerRepository.findOne({
      where: { id: layerId }
    });

    // 生成瓦片图像
    return this.renderTile(features, layer?.style, bbox, zoom);
  }

  /**
   * 生成底图瓦片
   */
  private async generateBaseTile(bbox: [number, number, number, number], zoom: number): Promise<Buffer> {
    // 这里可以集成第三方底图服务
    // 或者生成简单的底图
    return this.generateSimpleBaseTile(bbox, zoom);
  }

  /**
   * 渲染瓦片
   */
  private async renderTile(
    features: SpatialFeature[], 
    style: any, 
    bbox: [number, number, number, number], 
    zoom: number
  ): Promise<Buffer> {
    // 简化实现：生成一个包含要素信息的PNG瓦片
    // 实际应用中应该使用专业的地图渲染库如Mapnik
    
    const tileSize = 256;
    const canvas = this.createCanvas(tileSize, tileSize);
    const ctx = canvas.getContext('2d');

    // 设置背景
    ctx.fillStyle = '#f8f8f8';
    ctx.fillRect(0, 0, tileSize, tileSize);

    // 绘制要素
    for (const feature of features) {
      try {
        const geometry = JSON.parse(feature.geometry);
        this.drawGeometry(ctx, geometry, style, bbox, tileSize);
      } catch (error) {
        this.logger.warn(`绘制要素失败: ${feature.id}`, error);
      }
    }

    // 转换为PNG Buffer
    return canvas.toBuffer('image/png');
  }

  /**
   * 创建画布
   */
  private createCanvas(width: number, height: number): any {
    // 这里需要使用canvas库，如node-canvas
    // 简化实现，返回模拟对象
    return {
      getContext: () => ({
        fillStyle: '',
        strokeStyle: '',
        lineWidth: 1,
        fillRect: () => {},
        strokeRect: () => {},
        beginPath: () => {},
        moveTo: () => {},
        lineTo: () => {},
        closePath: () => {},
        fill: () => {},
        stroke: () => {}
      }),
      toBuffer: () => Buffer.alloc(0)
    };
  }

  /**
   * 绘制几何图形
   */
  private drawGeometry(ctx: any, geometry: any, style: any, bbox: [number, number, number, number], tileSize: number) {
    const coords = this.projectCoordinates(geometry.coordinates, bbox, tileSize);
    
    // 设置样式
    if (style?.fill?.color) {
      ctx.fillStyle = style.fill.color;
    }
    if (style?.stroke?.color) {
      ctx.strokeStyle = style.stroke.color;
      ctx.lineWidth = style.stroke.width || 1;
    }

    switch (geometry.type) {
      case 'Point':
        this.drawPoint(ctx, coords);
        break;
      case 'LineString':
        this.drawLineString(ctx, coords);
        break;
      case 'Polygon':
        this.drawPolygon(ctx, coords);
        break;
      case 'MultiPoint':
        coords.forEach(coord => this.drawPoint(ctx, coord));
        break;
      case 'MultiLineString':
        coords.forEach(coord => this.drawLineString(ctx, coord));
        break;
      case 'MultiPolygon':
        coords.forEach(coord => this.drawPolygon(ctx, coord));
        break;
    }
  }

  /**
   * 投影坐标到瓦片像素坐标
   */
  private projectCoordinates(coordinates: any, bbox: [number, number, number, number], tileSize: number): any {
    const [minX, minY, maxX, maxY] = bbox;
    const scaleX = tileSize / (maxX - minX);
    const scaleY = tileSize / (maxY - minY);

    const projectPoint = (coord: [number, number]) => [
      (coord[0] - minX) * scaleX,
      tileSize - (coord[1] - minY) * scaleY // 翻转Y轴
    ];

    if (Array.isArray(coordinates[0])) {
      if (Array.isArray(coordinates[0][0])) {
        // 多维数组
        return coordinates.map(ring => ring.map(projectPoint));
      } else {
        // 二维数组
        return coordinates.map(projectPoint);
      }
    } else {
      // 一维数组（点坐标）
      return projectPoint(coordinates);
    }
  }

  /**
   * 绘制点
   */
  private drawPoint(ctx: any, coords: [number, number]) {
    const radius = 3;
    ctx.beginPath();
    ctx.arc(coords[0], coords[1], radius, 0, 2 * Math.PI);
    ctx.fill();
  }

  /**
   * 绘制线
   */
  private drawLineString(ctx: any, coords: [number, number][]) {
    if (coords.length < 2) return;

    ctx.beginPath();
    ctx.moveTo(coords[0][0], coords[0][1]);
    
    for (let i = 1; i < coords.length; i++) {
      ctx.lineTo(coords[i][0], coords[i][1]);
    }
    
    ctx.stroke();
  }

  /**
   * 绘制多边形
   */
  private drawPolygon(ctx: any, coords: [number, number][][]) {
    if (coords.length === 0) return;

    // 绘制外环
    const exterior = coords[0];
    if (exterior.length < 3) return;

    ctx.beginPath();
    ctx.moveTo(exterior[0][0], exterior[0][1]);
    
    for (let i = 1; i < exterior.length; i++) {
      ctx.lineTo(exterior[i][0], exterior[i][1]);
    }
    
    ctx.closePath();
    ctx.fill();
    ctx.stroke();

    // 绘制内环（洞）
    for (let i = 1; i < coords.length; i++) {
      const hole = coords[i];
      if (hole.length < 3) continue;

      ctx.beginPath();
      ctx.moveTo(hole[0][0], hole[0][1]);
      
      for (let j = 1; j < hole.length; j++) {
        ctx.lineTo(hole[j][0], hole[j][1]);
      }
      
      ctx.closePath();
      // 使用复合操作创建洞
      ctx.globalCompositeOperation = 'destination-out';
      ctx.fill();
      ctx.globalCompositeOperation = 'source-over';
    }
  }

  /**
   * 生成简单底图瓦片
   */
  private generateSimpleBaseTile(bbox: [number, number, number, number], zoom: number): Buffer {
    const tileSize = 256;
    const canvas = this.createCanvas(tileSize, tileSize);
    const ctx = canvas.getContext('2d');

    // 绘制简单的网格底图
    ctx.fillStyle = '#e6f3ff';
    ctx.fillRect(0, 0, tileSize, tileSize);

    // 绘制网格线
    ctx.strokeStyle = '#cccccc';
    ctx.lineWidth = 1;

    const gridSize = 32;
    for (let i = 0; i <= tileSize; i += gridSize) {
      ctx.beginPath();
      ctx.moveTo(i, 0);
      ctx.lineTo(i, tileSize);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(0, i);
      ctx.lineTo(tileSize, i);
      ctx.stroke();
    }

    return canvas.toBuffer('image/png');
  }

  /**
   * 生成空白瓦片
   */
  private generateEmptyTile(): Buffer {
    const tileSize = 256;
    const canvas = this.createCanvas(tileSize, tileSize);
    const ctx = canvas.getContext('2d');

    // 透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0)';
    ctx.fillRect(0, 0, tileSize, tileSize);

    return canvas.toBuffer('image/png');
  }

  /**
   * 获取瓦片缓存键
   */
  private getTileCacheKey(z: number, x: number, y: number, layerId?: string): string {
    return `tile:${z}:${x}:${y}:${layerId || 'base'}`;
  }

  /**
   * 清除瓦片缓存
   */
  async clearTileCache(layerId?: string) {
    // 这里应该实现缓存清除逻辑
    this.logger.log(`清除瓦片缓存: ${layerId || 'all'}`);
  }
}
