/**
 * 空间分析控制器
 */
import { Controller, Post, Get, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { SpatialAnalysisService } from './spatial-analysis.service';
import { BufferAnalysisService } from './buffer-analysis.service';
import { OverlayAnalysisService } from './overlay-analysis.service';
import { NetworkAnalysisService } from './network-analysis.service';
import { StatisticalAnalysisService } from './statistical-analysis.service';
import {
  BufferAnalysisDto,
  OverlayAnalysisDto,
  NetworkAnalysisDto,
  StatisticalAnalysisDto,
  ProximityAnalysisDto,
  HotspotAnalysisDto,
  InterpolationDto
} from './dto/analysis.dto';

@ApiTags('空间分析')
@Controller('spatial-analysis')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SpatialAnalysisController {
  constructor(
    private readonly spatialAnalysisService: SpatialAnalysisService,
    private readonly bufferAnalysisService: BufferAnalysisService,
    private readonly overlayAnalysisService: OverlayAnalysisService,
    private readonly networkAnalysisService: NetworkAnalysisService,
    private readonly statisticalAnalysisService: StatisticalAnalysisService,
  ) {}

  @Post('buffer')
  @ApiOperation({ summary: '缓冲区分析' })
  @ApiResponse({ status: 200, description: '缓冲区分析结果' })
  async bufferAnalysis(@Body() bufferAnalysisDto: BufferAnalysisDto) {
    return this.bufferAnalysisService.performBufferAnalysis(bufferAnalysisDto);
  }

  @Post('overlay')
  @ApiOperation({ summary: '叠加分析' })
  @ApiResponse({ status: 200, description: '叠加分析结果' })
  async overlayAnalysis(@Body() overlayAnalysisDto: OverlayAnalysisDto) {
    return this.overlayAnalysisService.performOverlayAnalysis(overlayAnalysisDto);
  }

  @Post('network')
  @ApiOperation({ summary: '网络分析' })
  @ApiResponse({ status: 200, description: '网络分析结果' })
  async networkAnalysis(@Body() networkAnalysisDto: NetworkAnalysisDto) {
    return this.networkAnalysisService.performNetworkAnalysis(networkAnalysisDto);
  }

  @Post('statistical')
  @ApiOperation({ summary: '统计分析' })
  @ApiResponse({ status: 200, description: '统计分析结果' })
  async statisticalAnalysis(@Body() statisticalAnalysisDto: StatisticalAnalysisDto) {
    return this.statisticalAnalysisService.performStatisticalAnalysis(statisticalAnalysisDto);
  }

  @Post('proximity')
  @ApiOperation({ summary: '邻近分析' })
  @ApiResponse({ status: 200, description: '邻近分析结果' })
  async proximityAnalysis(@Body() proximityAnalysisDto: ProximityAnalysisDto) {
    return this.spatialAnalysisService.performProximityAnalysis(proximityAnalysisDto);
  }

  @Post('hotspot')
  @ApiOperation({ summary: '热点分析' })
  @ApiResponse({ status: 200, description: '热点分析结果' })
  async hotspotAnalysis(@Body() hotspotAnalysisDto: HotspotAnalysisDto) {
    return this.spatialAnalysisService.performHotspotAnalysis(hotspotAnalysisDto);
  }

  @Post('interpolation')
  @ApiOperation({ summary: '空间插值' })
  @ApiResponse({ status: 200, description: '插值分析结果' })
  async interpolationAnalysis(@Body() interpolationDto: InterpolationDto) {
    return this.spatialAnalysisService.performInterpolation(interpolationDto);
  }

  @Get('capabilities')
  @ApiOperation({ summary: '获取分析能力' })
  @ApiResponse({ status: 200, description: '分析能力列表' })
  async getAnalysisCapabilities() {
    return this.spatialAnalysisService.getAnalysisCapabilities();
  }

  @Get('history/:projectId')
  @ApiOperation({ summary: '获取分析历史' })
  @ApiResponse({ status: 200, description: '分析历史记录' })
  async getAnalysisHistory(
    @Param('projectId') projectId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10
  ) {
    return this.spatialAnalysisService.getAnalysisHistory(projectId, page, limit);
  }

  @Get('result/:analysisId')
  @ApiOperation({ summary: '获取分析结果' })
  @ApiResponse({ status: 200, description: '分析结果详情' })
  async getAnalysisResult(@Param('analysisId') analysisId: string) {
    return this.spatialAnalysisService.getAnalysisResult(analysisId);
  }
}
