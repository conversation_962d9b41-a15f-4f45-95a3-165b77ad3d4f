const { spawn } = require('child_process');
const http = require('http');

console.log('🚀 开始测试 Signaling Service...\n');

// 启动服务
console.log('📦 启动服务...');
const serverProcess = spawn('node', ['dist/main.js'], {
  cwd: __dirname,
  stdio: 'pipe',
  env: { ...process.env, PORT: 3001 }
});

let serverReady = false;

serverProcess.stdout.on('data', (data) => {
  const output = data.toString();
  console.log('服务输出:', output);
  if (output.includes('信令服务已启动')) {
    serverReady = true;
    runTests();
  }
});

serverProcess.stderr.on('data', (data) => {
  console.error('服务错误:', data.toString());
});

// 等待服务启动
setTimeout(() => {
  if (!serverReady) {
    console.log('⏰ 服务启动超时，尝试运行测试...');
    runTests();
  }
}, 5000);

async function runTests() {
  console.log('\n🧪 开始运行测试...\n');
  
  try {
    // 测试1: 健康检查
    console.log('1️⃣ 测试健康检查...');
    await testHealthCheck();
    console.log('✅ 健康检查通过\n');

    // 测试2: 创建会话
    console.log('2️⃣ 测试创建会话...');
    const session = await testCreateSession();
    console.log('✅ 会话创建成功:', session.data.id, '\n');

    // 测试3: WebSocket连接
    console.log('3️⃣ 测试WebSocket连接...');
    await testWebSocketConnection();
    console.log('✅ WebSocket连接测试通过\n');

    // 测试4: 性能指标
    console.log('4️⃣ 测试性能指标...');
    await testMetrics();
    console.log('✅ 性能指标测试通过\n');

    console.log('🎉 所有测试通过！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    // 清理
    console.log('\n🧹 清理资源...');
    serverProcess.kill();
    process.exit(0);
  }
}

function testHealthCheck() {
  return new Promise((resolve, reject) => {
    const req = http.get('http://localhost:3001/signaling/health', (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (result.status === 'ok') {
            resolve(result);
          } else {
            reject(new Error('健康检查失败'));
          }
        } catch (error) {
          reject(error);
        }
      });
    });
    
    req.on('error', reject);
    req.setTimeout(5000, () => reject(new Error('健康检查超时')));
  });
}

function testCreateSession() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      name: '测试会话',
      description: '这是一个测试会话',
      maxParticipants: 5,
      type: 'webrtc'
    });

    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/signaling/sessions',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (result.success) {
            resolve(result);
          } else {
            reject(new Error('创建会话失败'));
          }
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(5000, () => reject(new Error('创建会话超时')));
    req.write(postData);
    req.end();
  });
}

function testWebSocketConnection() {
  return new Promise((resolve, reject) => {
    const socket = io('http://localhost:3001/signaling', {
      transports: ['websocket']
    });

    const timeout = setTimeout(() => {
      socket.close();
      reject(new Error('WebSocket连接超时'));
    }, 5000);

    socket.on('connect', () => {
      console.log('   WebSocket已连接');
      
      // 测试ping-pong
      const timestamp = Date.now();
      socket.emit('ping', timestamp);
      
      socket.on('pong', (receivedTimestamp) => {
        if (receivedTimestamp === timestamp) {
          console.log('   Ping-Pong测试通过');
          clearTimeout(timeout);
          socket.close();
          resolve();
        } else {
          clearTimeout(timeout);
          socket.close();
          reject(new Error('Ping-Pong测试失败'));
        }
      });
    });

    socket.on('connect_error', (error) => {
      clearTimeout(timeout);
      reject(new Error(`WebSocket连接错误: ${error.message}`));
    });
  });
}

function testMetrics() {
  return new Promise((resolve, reject) => {
    const req = http.get('http://localhost:3001/monitoring/metrics', (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (result.success && result.data) {
            console.log('   指标数据:', {
              connectedPeers: result.data.signaling?.connectedPeers || 0,
              activeSessions: result.data.signaling?.activeSessions || 0
            });
            resolve(result);
          } else {
            reject(new Error('获取指标失败'));
          }
        } catch (error) {
          reject(error);
        }
      });
    });
    
    req.on('error', reject);
    req.setTimeout(5000, () => reject(new Error('获取指标超时')));
  });
}
