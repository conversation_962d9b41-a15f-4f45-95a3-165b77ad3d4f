/**
 * WFS服务
 */
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class WfsService {
  private readonly logger = new Logger(WfsService.name);

  async handleRequest(query: any) {
    this.logger.log(`WFS请求: ${JSON.stringify(query)}`);
    
    const request = query.REQUEST?.toUpperCase();
    
    switch (request) {
      case 'GETCAPABILITIES':
        return this.getCapabilities();
      case 'DESCRIBEFEATURETYPE':
        return this.describeFeatureType(query);
      case 'GETFEATURE':
        return this.getFeature(query);
      default:
        return {
          contentType: 'application/xml',
          data: this.createErrorResponse('InvalidRequest', '不支持的请求类型')
        };
    }
  }

  private getCapabilities() {
    const capabilities = `<?xml version="1.0" encoding="UTF-8"?>
<wfs:WFS_Capabilities version="2.0.0">
  <ows:ServiceIdentification>
    <ows:Title>Spatial Service WFS</ows:Title>
    <ows:Abstract>空间信息系统WFS服务</ows:Abstract>
    <ows:ServiceType>WFS</ows:ServiceType>
    <ows:ServiceTypeVersion>2.0.0</ows:ServiceTypeVersion>
  </ows:ServiceIdentification>
  <ows:OperationsMetadata>
    <ows:Operation name="GetCapabilities"/>
    <ows:Operation name="DescribeFeatureType"/>
    <ows:Operation name="GetFeature"/>
  </ows:OperationsMetadata>
</wfs:WFS_Capabilities>`;

    return {
      contentType: 'application/xml',
      data: capabilities
    };
  }

  private describeFeatureType(query: any) {
    return {
      contentType: 'application/xml',
      data: '<?xml version="1.0" encoding="UTF-8"?><schema></schema>'
    };
  }

  private getFeature(query: any) {
    return {
      contentType: 'application/json',
      data: JSON.stringify({
        type: 'FeatureCollection',
        features: []
      })
    };
  }

  private createErrorResponse(code: string, message: string) {
    return `<?xml version="1.0" encoding="UTF-8"?>
<ows:ExceptionReport version="2.0.0">
  <ows:Exception exceptionCode="${code}">
    <ows:ExceptionText>${message}</ows:ExceptionText>
  </ows:Exception>
</ows:ExceptionReport>`;
  }
}
